import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useStickingQuery } from 'api/boekestyn-sticking-service';
import { StickingOrders } from '@/components/boekestyns/admin/sticking-orders';
import { StickingSchedules } from '@/components/boekestyns/admin/sticking-schedules';
import { setLastSelected } from '@/components/boekestyns/admin/admin-slice';
import { useAppDispatch } from '@/services/hooks';
import { routes } from '@/services/routes';
import { AdminLayout } from '../../../components/boekestyns/admin/layout';

export default function Sticking() {
  const dispatch = useAppDispatch();

  dispatch(setLastSelected(routes.boekestyns.admin.sticking.to()));

  useStickingQuery();

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="flex h-full flex-col overflow-y-auto bg-blue-500">
        <div className="m-4 flex flex-grow flex-col overflow-y-auto rounded bg-white">
          <div className="grid h-full grid-cols-2 gap-x-2 overflow-y-auto p-2">
            <div className="flex h-full flex-col overflow-y-auto rounded border">
              <StickingOrders />
            </div>
            <div className="flex h-full flex-col overflow-y-auto rounded border">
              <StickingSchedules />
            </div>
          </div>
        </div>
      </div>
    </DndProvider>
  );
}

Sticking.getLayout = function getLayout(page: React.ReactElement) {
  return <AdminLayout>{page}</AdminLayout>;
};
