import { useMemo } from 'react';
import * as boeks from 'api/models/boekestyns';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';
import { formatNumber } from '@/utils/format';
import { Timing } from './timing';

export function SpacingItem({
  workOrder,
}: {
  workOrder: boeks.SpacingWorkOrderItem;
}) {
  const labour = workOrder.labour,
    inProcess = useMemo(() => labour.find((l) => !l.endTime) || null, [labour]),
    isFinalLabour = useMemo(() => labour.some((l) => l.finalLabour), [labour]),
    estimatedHours = useMemo(
      () =>
        workOrder.spacingPotsPerHour
          ? workOrder.potsToSpace / workOrder.spacingPotsPerHour
          : 0,
      [workOrder]
    ),
    fromTables = useMemo(() => {
      const cuttings = workOrder.potsToSpace * workOrder.cuttingsPerPot,
        perTable =
          workOrder.fromSpaceType === 'Tight'
            ? workOrder.cuttingsPerTableTight
            : workOrder.fromSpaceType === 'Partial'
            ? workOrder.cuttingsPerTablePartiallySpaced || 0
            : workOrder.cuttingsPerTableSpaced,
        tables = perTable ? cuttings / perTable : 0;
      return tables;
    }, [workOrder]),
    toTables = useMemo(() => {
      const cuttings = workOrder.potsToSpace * workOrder.cuttingsPerPot,
        perTable =
          workOrder.toSpaceType === 'Tight'
            ? workOrder.cuttingsPerTableTight
            : workOrder.toSpaceType === 'Partial'
            ? workOrder.cuttingsPerTablePartiallySpaced || 0
            : workOrder.cuttingsPerTableSpaced,
        tables = perTable ? cuttings / perTable : 0;
      return tables;
    }, [workOrder]);

  return (
    <tr
      className={classNames(
        'border-b border-gray-200',
        isFinalLabour ? 'bg-green-600' : ''
      )}
    >
      <td
        className={classNames(
          'p-2 text-left align-top text-gray-900',
          inProcess ? 'border-b-4 border-t-4 border-blue-600' : ''
        )}
      >
        <Timing workOrder={workOrder} />
      </td>
      <td
        className={classNames(
          'p-2 text-left align-top text-gray-900',
          inProcess ? 'border-b-4 border-t-4 border-blue-600' : ''
        )}
      >
        <span className="font-semibold">{workOrder.orderNumber}</span>
        {!!workOrder.orderComments && (
          <div className="ml-2 italic">{workOrder.orderComments}</div>
        )}
        {!!workOrder.spacingComments && (
          <div className="ml-2 italic">{workOrder.spacingComments}</div>
        )}
      </td>
      <td
        className={classNames(
          'p-2 text-left align-top text-gray-900',
          inProcess ? 'border-b-4 border-t-4 border-blue-600' : ''
        )}
      >
        <span className="font-semibold">
          {workOrder.plantSize}&nbsp;{workOrder.plantCrop}&nbsp;
          {workOrder.customer}
        </span>
        {!isFinalLabour && !!workOrder.varieties.length && (
          <div className="ml-2 italic">
            {workOrder.varieties.map((variety: any) => (
              <div key={variety.name}>
                {variety.name}: {formatNumber(variety.pots)}
              </div>
            ))}
          </div>
        )}
      </td>
      <td
        className={classNames(
          'p-2 text-center align-top text-gray-900',
          inProcess ? 'border-b-4 border-t-4 border-blue-600' : ''
        )}
      >
        {workOrder.requiresPinching && <Icon icon="check-square" />}
      </td>
      <td
        className={classNames(
          'p-2 text-center align-top text-gray-900',
          inProcess ? 'border-b-4 border-t-4 border-blue-600' : ''
        )}
      >
        <div className="flex flex-row">
          <div className="text-right">
            {workOrder.fromSpaceType}
            <br />
            {formatNumber(fromTables, '0,0.00')} tables
          </div>
          <Icon icon="arrow-right" className="mx-1" />
          <div>
            {workOrder.toSpaceType}
            <br />
            {formatNumber(toTables, '0,0.00')} tables
          </div>
        </div>
      </td>
      <td
        className={classNames(
          'p-2 text-right align-top text-gray-900',
          inProcess ? 'border-b-4 border-t-4 border-blue-600' : ''
        )}
      >
        {formatNumber(workOrder.potsToSpace)}
      </td>
      <td
        className={classNames(
          'p-2 text-right align-top text-gray-900',
          inProcess ? 'border-b-4 border-t-4 border-blue-600' : ''
        )}
      >
        {estimatedHours}
      </td>
    </tr>
  );
}
