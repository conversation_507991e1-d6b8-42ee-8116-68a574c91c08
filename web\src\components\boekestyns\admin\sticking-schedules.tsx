import { Fragment, useEffect, useState, useMemo, use } from 'react';
import moment from 'moment';
import * as HeadlessUI from '@headlessui/react';
import { useDebounce } from 'use-debounce';
import { useStickingSchedulesQuery } from 'api/boekestyn-sticking-service';
import { Icon } from '@/components/icon';
import { useAppSelector, useAppDispatch } from '@/services/hooks';
import {
  selectScheduleWeek,
  selectScheduleYear,
  selectScheduleDate,
  setScheduleWeek,
  setScheduleDate,
  previousScheduleWeek,
  nextScheduleWeek,
  selectWeekDates,
  selectSelectedIndex,
} from '@/components/boekestyns/admin/admin-slice';
import { handleFocus } from '@/utils/focus';
import { classNames } from '@/utils/class-names';
import { StickingScheduleDay } from './sticking-schedule-day';

export function StickingSchedules() {
  const dispatch = useAppDispatch(),
    scheduleWeek = useAppSelector(selectScheduleWeek),
    scheduleYear = useAppSelector(selectScheduleYear),
    scheduleDate = useAppSelector(selectScheduleDate),
    [week, setWeek] = useState(scheduleWeek.toString()),
    [year, setYear] = useState(scheduleYear.toString()),
    [weekDebounced, { flush: flushWeek, isPending: weekPending }] = useDebounce(
      week,
      500
    ),
    [yearDebounced, { flush: flushYear, isPending: yearPending }] = useDebounce(
      year,
      500
    ),
    scheduleDates = useAppSelector(selectWeekDates),
    selectedIndex = useAppSelector(selectSelectedIndex),
    isPending = useMemo(
      () => weekPending() || yearPending(),
      [weekPending, yearPending]
    );

  useStickingSchedulesQuery({
    date: scheduleDate,
  });

  useEffect(() => {
    if (weekDebounced && yearDebounced) {
      const week = parseInt(weekDebounced, 10),
        year = parseInt(yearDebounced, 10);
      dispatch(setScheduleWeek({ week, year }));
    }
  }, [weekDebounced, dispatch, yearDebounced]);

  useEffect(() => {
    if (scheduleWeek) {
      setWeek(scheduleWeek.toString());
      flushWeek();
    }
  }, [scheduleWeek, flushWeek]);

  useEffect(() => {
    if (scheduleYear) {
      setYear(scheduleYear.toString());
      flushYear();
    }
  }, [scheduleYear, flushYear]);

  const handleWeekChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setWeek(event.target.value);
  };

  const handleYearChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setYear(event.target.value);
  };

  const handleNextWeek = () => {
    dispatch(nextScheduleWeek());
  };

  const handlePreviousWeek = () => {
    dispatch(previousScheduleWeek());
  };

  const handleTabChange = (index: number) => {
    const date = scheduleDates[index] ?? scheduleDates[0];
    dispatch(setScheduleDate(date));
  };

  return (
    <HeadlessUI.Tab.Group
      as="div"
      selectedIndex={selectedIndex}
      onChange={handleTabChange}
      className="flex flex-col overflow-y-auto"
    >
      <div className="flex flex-col p-2 2xl:flex-row">
        <div className="flex flex-row justify-center gap-4">
          <div className="flex items-end">
            <button
              type="button"
              className="btn-secondary"
              onClick={handlePreviousWeek}
            >
              <Icon
                icon={isPending ? 'spinner' : 'chevron-left'}
                spin={isPending}
              />
            </button>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-500">
              Week
            </label>
            <input
              type="number"
              min="1"
              max="53"
              value={week}
              onChange={handleWeekChange}
              onFocus={handleFocus}
              className="w-16 rounded-md border-gray-300 text-center text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-500">
              Year
            </label>
            <input
              type="number"
              min="2020"
              max="2050"
              value={year}
              onChange={handleYearChange}
              onFocus={handleFocus}
              className="w-20 rounded-md border-gray-300 text-center text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>
          <div className="flex items-end">
            <button
              type="button"
              className="btn-secondary"
              onClick={handleNextWeek}
            >
              <Icon
                icon={isPending ? 'spinner' : 'chevron-right'}
                spin={isPending}
              />
            </button>
          </div>
        </div>
        <HeadlessUI.Tab.List
          as="nav"
          className="mx-4 flex flex-grow justify-around space-x-8"
        >
          {scheduleDates.map((date) => (
            <HeadlessUI.Tab key={date} as={Fragment}>
              {({ selected }) => (
                <button
                  type="button"
                  className={classNames(
                    'text-nowrap rounded-lg px-4 py-2 text-center text-sm font-medium',
                    selected
                      ? 'bg-blue-500 text-white'
                      : 'text-gray-500 hover:text-blue-500'
                  )}
                >
                  {moment(date).format('ddd')}
                  <div className="text-xs italic">
                    {moment(date).format('MMM D')}
                  </div>
                </button>
              )}
            </HeadlessUI.Tab>
          ))}
        </HeadlessUI.Tab.List>
      </div>
      <HeadlessUI.Tab.Panels
        as="div"
        className="flex flex-grow flex-col overflow-y-auto border-t"
      >
        {scheduleDates.map((date) => (
          <HeadlessUI.Tab.Panel
            key={date}
            className="flex flex-grow flex-col overflow-y-auto"
          >
            <StickingScheduleDay key={date} date={date} />
          </HeadlessUI.Tab.Panel>
        ))}
      </HeadlessUI.Tab.Panels>
    </HeadlessUI.Tab.Group>
  );
}
