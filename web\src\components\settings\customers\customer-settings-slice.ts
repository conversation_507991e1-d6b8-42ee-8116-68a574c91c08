import {
  createAction,
  createAsyncThunk,
  createSlice,
  AsyncThunk,
  PayloadAction,
  createSelector,
} from '@reduxjs/toolkit';
import { ProblemDetails } from '@/utils/problem-details';
import {
  CustomerSettingsResponse,
  settingsService,
  UpdateProductCustomerDefaultResponse,
  UpdateProductShipToDefaultResponse,
} from 'api/settings-service';
import { spireApi } from 'api/spire-service';
import * as futureOrders from 'api/models/future-orders';
import * as prebooks from 'api/models/prebooks';
import * as spire from 'api/models/spire';
import { RootState } from '@/services/store';
import { contains } from '@/utils/equals';

export interface CustomerSettingsState {
  isLoading: boolean;
  error: ProblemDetails | null;
  query: string;
  customer: spire.Customer | null;
  shipTos: spire.CustomerShipTo[];
  customerItemCodeByShipTo: boolean;
  productCustomerDefaults: futureOrders.ProductCustomerDefault[];
  productShipToDefaults: prebooks.ProductShipToDefault[];
  potCovers: string[];
  inventoryItems: spire.InventoryItem[];
}

const initialState: CustomerSettingsState = {
  isLoading: false,
  error: null,
  query: '',
  customer: null,
  shipTos: [],
  customerItemCodeByShipTo: false,
  productCustomerDefaults: [],
  productShipToDefaults: [],
  potCovers: [],
  inventoryItems: [],
};

export const setCustomer: AsyncThunk<
  CustomerSettingsResponse | null,
  spire.Customer | null,
  { state: RootState }
> = createAsyncThunk(
  'customer-settings-setCustomer',
  async (customer, { rejectWithValue, dispatch }) => {
    try {
      dispatch(customerSettingsSlice.actions.clearState());

      return customer
        ? await settingsService.customerSettings(customer.id)
        : null;
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const addPotCover: AsyncThunk<
  string | undefined,
  string,
  { state: RootState }
> = createAsyncThunk(
  'customer-settings-addPotCover',
  async (potCover, { rejectWithValue, getState }) => {
    try {
      const rootState = getState() as RootState,
        customer = rootState.customerSettings.customer;

      if (customer) {
        await settingsService.addPotCover(customer.id, potCover);

        return potCover;
      }
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const deletePotCover: AsyncThunk<
  string | undefined,
  string,
  { state: RootState }
> = createAsyncThunk(
  'customer-settings-deletePotCover',
  async (potCover, { rejectWithValue, getState }) => {
    try {
      const rootState = getState() as RootState,
        customer = rootState.customerSettings.customer;

      if (customer) {
        await settingsService.deletePotCover(customer.id, potCover);

        return potCover;
      }
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export interface SetCustomerItemCodeByShipToArgs {
  customerId: number;
  customerItemCodeByShipTo: boolean;
}

export const setCustomerItemCodeByShipTo: AsyncThunk<
  CustomerSettingsResponse,
  SetCustomerItemCodeByShipToArgs,
  { state: RootState }
> = createAsyncThunk(
  'customer-settings-setCustomerItemCodeByShipTo',
  async (args, { rejectWithValue }) => {
    try {
      const model = { customerItemCodeByShipTo: args.customerItemCodeByShipTo };
      return await settingsService.defaults(args.customerId, model);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const deleteProductCustomerDefault: AsyncThunk<
  number,
  number,
  { state: RootState }
> = createAsyncThunk(
  'customer-settings-deleteProductCustomerDefault',
  async (id, { rejectWithValue }) => {
    try {
      await settingsService.deleteProductCustomerDefault(id);
      return id;
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

interface UpdateProductCustomerDefaultArgs {
  id: number;
  customerItemCode: string;
}
export const updateProductCustomerDefault: AsyncThunk<
  UpdateProductCustomerDefaultResponse,
  UpdateProductCustomerDefaultArgs,
  { state: RootState }
> = createAsyncThunk(
  'customer-settings-updateProductCustomerDefault',
  async ({ id, customerItemCode }, { rejectWithValue }) => {
    try {
      return await settingsService.updateProductCustomerDefault({
        id,
        customerItemCode,
      });
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const deleteProductShipToDefault: AsyncThunk<
  number,
  number,
  { state: RootState }
> = createAsyncThunk(
  'customer-settings-deleteProductShipToDefault',
  async (id, { rejectWithValue }) => {
    try {
      await settingsService.deleteProductShipToDefault(id);
      return id;
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

interface UpdateProductShipToDefaultArgs {
  id: number;
  hasPotCover: boolean;
  potCover: string | null;
  upc: string | null;
  weightsAndMeasures: boolean;
  retail: string | null;
  unitPrice: number | null;
  customerItemCode: string | null;
}
export const updateProductShipToDefault: AsyncThunk<
  UpdateProductShipToDefaultResponse,
  UpdateProductShipToDefaultArgs,
  { state: RootState }
> = createAsyncThunk(
  'customer-settings-updateProductShipToDefault',
  async (args, { rejectWithValue }) => {
    try {
      return await settingsService.updateProductShipToDefault(args);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

const setCustomerPending = createAction(setCustomer.pending.type),
  setCustomerFulfilled = createAction<CustomerSettingsResponse | null>(
    setCustomer.fulfilled.type
  ),
  setCustomerRejected = createAction<ProblemDetails>(setCustomer.rejected.type),
  setCustomerItemCodeByShipToPending = createAction(
    setCustomerItemCodeByShipTo.pending.type
  ),
  setCustomerItemCodeByShipToFulfilled = createAction<CustomerSettingsResponse>(
    setCustomerItemCodeByShipTo.fulfilled.type
  ),
  setCustomerItemCodeByShipToRejected = createAction<ProblemDetails>(
    setCustomerItemCodeByShipTo.rejected.type
  ),
  deleteProductCustomerDefaultFulfilled = createAction<number>(
    deleteProductCustomerDefault.fulfilled.type
  ),
  updateProductCustomerDefaultFulfilled =
    createAction<UpdateProductCustomerDefaultResponse>(
      updateProductCustomerDefault.fulfilled.type
    ),
  deleteProductCustomerDefaultRejected = createAction<ProblemDetails>(
    deleteProductCustomerDefault.rejected.type
  ),
  updateProductCustomerDefaultRejected = createAction<ProblemDetails>(
    updateProductCustomerDefault.rejected.type
  ),
  deleteProductShipToDefaultFulfilled = createAction<number>(
    deleteProductShipToDefault.fulfilled.type
  ),
  updateProductShipToDefaultFulfilled =
    createAction<UpdateProductShipToDefaultResponse>(
      updateProductShipToDefault.fulfilled.type
    ),
  deleteProductShipToDefaultRejected = createAction<ProblemDetails>(
    deleteProductShipToDefault.rejected.type
  ),
  updateProductShipToDefaultRejected = createAction<ProblemDetails>(
    updateProductShipToDefault.rejected.type
  ),
  addPotCoverFulfilled = createAction<string | undefined>(
    addPotCover.fulfilled.type
  ),
  addPotCoverRejected = createAction<ProblemDetails>(addPotCover.rejected.type),
  deletePotCoverFulfilled = createAction<string | undefined>(
    deletePotCover.fulfilled.type
  ),
  deletePotCoverRejected = createAction<ProblemDetails>(
    deletePotCover.rejected.type
  );

export const customerSettingsSlice = createSlice({
  name: 'customer-settings',
  initialState,
  reducers: {
    clearState(state) {
      Object.assign(state, { ...initialState });
    },
    clearError(state) {
      state.error = null;
    },
    setQuery(state, { payload }: PayloadAction<string>) {
      state.query = payload;
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(setCustomerPending, (state) => {
        state.isLoading = true;
      })
      .addCase(setCustomerFulfilled, (state, { payload, type }) => {
        state.isLoading = false;
        if (payload) {
          handleCustomerDetailFulfilled(state, { payload, type });
        } else {
          state.customer = null;
          state.customerItemCodeByShipTo = false;
          state.shipTos = [];
          state.productCustomerDefaults = [];
          state.productShipToDefaults = [];
          state.potCovers = [];
        }
      })
      .addCase(setCustomerRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addCase(setCustomerItemCodeByShipToPending, (state) => {
        state.isLoading = true;
      })
      .addCase(
        setCustomerItemCodeByShipToFulfilled,
        handleCustomerDetailFulfilled
      )
      .addCase(setCustomerItemCodeByShipToRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addCase(deleteProductCustomerDefaultFulfilled, (state, { payload }) => {
        const productCustomerDefaults = state.productCustomerDefaults
          .map((d) => ({ ...d }))
          .filter((d) => d.id !== payload);
        state.productCustomerDefaults = productCustomerDefaults;
      })
      .addCase(updateProductCustomerDefaultFulfilled, (state, { payload }) => {
        const productCustomerDefaults = state.productCustomerDefaults.map(
            (d) => ({ ...d })
          ),
          productCustomerDefault = productCustomerDefaults.find(
            (d) => d.id === payload.productCustomerDefault.id
          );
        if (productCustomerDefault) {
          productCustomerDefault.customerItemCode =
            payload.productCustomerDefault.customerItemCode;
        }
        state.productCustomerDefaults = productCustomerDefaults;
      })
      .addCase(deleteProductCustomerDefaultRejected, (state, { payload }) => {
        state.error = payload;
      })
      .addCase(updateProductCustomerDefaultRejected, (state, { payload }) => {
        state.error = payload;
      })
      .addCase(deleteProductShipToDefaultFulfilled, (state, { payload }) => {
        const productShipToDefaults = state.productShipToDefaults
          .map((d) => ({ ...d }))
          .filter((d) => d.id !== payload);
        state.productShipToDefaults = productShipToDefaults;
      })
      .addCase(updateProductShipToDefaultFulfilled, (state, { payload }) => {
        const productShipToDefaults = state.productShipToDefaults.map((d) => ({
            ...d,
          })),
          productShipToDefault = productShipToDefaults.find(
            (d) => d.id === payload.productShipToDefault.id
          );
        if (productShipToDefault) {
          productShipToDefault.hasPotCover =
            payload.productShipToDefault.hasPotCover;
          productShipToDefault.potCover = payload.productShipToDefault.potCover;
          productShipToDefault.upc = payload.productShipToDefault.upc;
          productShipToDefault.weightsAndMeasures =
            payload.productShipToDefault.weightsAndMeasures;
          productShipToDefault.retail = payload.productShipToDefault.retail;
          productShipToDefault.unitPrice =
            payload.productShipToDefault.unitPrice;
          productShipToDefault.customerItemCode =
            payload.productShipToDefault.customerItemCode;
        }
        state.productShipToDefaults = productShipToDefaults;
      })
      .addCase(deleteProductShipToDefaultRejected, (state, { payload }) => {
        state.error = payload;
      })
      .addCase(updateProductShipToDefaultRejected, (state, { payload }) => {
        state.error = payload;
      })
      .addCase(addPotCoverFulfilled, (state, { payload }) => {
        if (payload) {
          state.potCovers = state.potCovers.concat([payload]);
        }
      })
      .addCase(addPotCoverRejected, (state, { payload }) => {
        state.error = payload;
      })
      .addCase(deletePotCoverFulfilled, (state, { payload }) => {
        if (payload) {
          state.potCovers = state.potCovers.filter((pc) => pc !== payload);
        }
      })
      .addCase(deletePotCoverRejected, (state, { payload }) => {
        state.error = payload;
      })
      .addMatcher(
        spireApi.endpoints.customers.matchRejected,
        (state, { payload }) => {
          state.error = payload as ProblemDetails;
        }
      )
      .addMatcher(
        spireApi.endpoints.inventoryItems.matchFulfilled,
        (state, { payload }) => {
          state.inventoryItems = payload.inventoryItems;
        }
      ),
});

export const { clearState, clearError, setQuery } =
  customerSettingsSlice.actions;

export const selectIsLoading = (state: RootState) =>
  state.customerSettings.isLoading;
export const selectError = (state: RootState) => state.customerSettings.error;
export const selectQuery = (state: RootState) => state.customerSettings.query;
export const selectCustomer = (state: RootState) =>
  state.customerSettings.customer;
export const selectCustomerItemCodeByShipTo = (state: RootState) =>
  state.customerSettings.customerItemCodeByShipTo;
export const selectShipTos = (state: RootState) =>
  state.customerSettings.shipTos;
const selectAllProductCustomerDefaults = (state: RootState) =>
  state.customerSettings.productCustomerDefaults;
export const selectPotCovers = (state: RootState) =>
  state.customerSettings.potCovers;
const selectAllProductShipToDefaults = (state: RootState) =>
  state.customerSettings.productShipToDefaults;
const selectInventoryItems = (state: RootState) =>
  state.customerSettings.inventoryItems;

export const selectProductCustomerDefaults = createSelector(
  selectAllProductCustomerDefaults,
  selectInventoryItems,
  selectQuery,
  (productCustomerDefaults, inventoryItems, query) =>
    productCustomerDefaults.filter(
      (d) =>
        !query ||
        contains(d.customerItemCode, query) ||
        contains(
          inventoryItems.find((i) => i.id === d.spireInventoryId)?.partNo,
          query
        )
    )
);

export const selectProductShipToDefaults = createSelector(
  selectAllProductShipToDefaults,
  selectInventoryItems,
  selectQuery,
  (productShipToDefaults, inventoryItems, query) =>
    productShipToDefaults.filter(
      (d) =>
        !query ||
        contains(d.customerItemCode, query) ||
        contains(
          inventoryItems.find((i) => i.id === d.spireInventoryId)?.partNo,
          query
        )
    )
);

export default customerSettingsSlice.reducer;

function handleCustomerDetailFulfilled(
  state: CustomerSettingsState,
  { payload }: PayloadAction<CustomerSettingsResponse>
) {
  state.isLoading = false;

  state.customer = payload.customer;
  state.customerItemCodeByShipTo = payload.customerItemCodeByShipTo;
  state.shipTos = payload.customer.shippingAddresses;
  state.productCustomerDefaults = payload.productCustomerDefaults;
  state.productShipToDefaults = payload.productShipToDefaults;
  state.potCovers = payload.potCovers;
}
