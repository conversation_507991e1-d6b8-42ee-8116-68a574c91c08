import { useRef, useState, useEffect, useMemo } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import {
  useDeleteHarvestingWorkOrderMutation,
  useSortHarvestingWorkOrdersMutation,
  useUpdateHarvestingWorkOrderCommentMutation,
  useHarvestingOrdersQuery,
} from 'api/boekestyn-harvesting-service';
import * as models from 'api/models/boekestyns';
import { Icon } from '@/components/icon';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { formatNumber } from '@/utils/format';
import { selectStartDate, selectEndDate } from './admin-slice';
import { moveItem, SortHarvestingWorkOrderType } from './harvesting-slice';

interface HarvestingScheduleWorkOrderProps {
  scheduleId: number;
  order: models.HarvestingWorkOrder;
}

export function HarvestingScheduleWorkOrder({
  scheduleId,
  order,
}: HarvestingScheduleWorkOrderProps) {
  const dispatch = useAppDispatch(),
    startDate = useAppSelector(selectStartDate),
    endDate = useAppSelector(selectEndDate),
    [comment, setComment] = useState(order.harvestingComments ?? ''),
    [deleteWorkOrder] = useDeleteHarvestingWorkOrderMutation(),
    [sortWorkOrders] = useSortHarvestingWorkOrdersMutation(),
    [updateWorkOrderComment] = useUpdateHarvestingWorkOrderCommentMutation(),
    estimatedHours = useMemo(() => order.estimatedHours, [order]),
    ref = useRef<HTMLTableRowElement>(null),
    cellClassName = 'whitespace-nowrap px-2 py-1 text-gray-700 align-top',
    [, drag] = useDrag(() => ({
      type: `${SortHarvestingWorkOrderType}-${scheduleId}`,
      item: order,
    })),
    [{ isOver }, drop] = useDrop<
      models.HarvestingWorkOrder,
      void,
      { isOver: boolean }
    >(() => ({
      accept: `${SortHarvestingWorkOrderType}-${scheduleId}`,
      collect: (monitor) => ({
        isOver: monitor.isOver(),
      }),
      drop(droppedItem) {
        dispatch(
          moveItem({ scheduleId, existingItem: order, movingItem: droppedItem })
        ).then(({ payload }) => {
          const workOrders = payload as models.HarvestingWorkOrder[] | undefined;
          if (workOrders) {
            const args = {
              workOrders: workOrders.map((wo) => ({
                workOrderId: wo.id,
                sortOrder: wo.sortOrder,
              })),
            };
            console.log(args);
            sortWorkOrders(args);
          }
        });
      },
    })),
    { refetch: refetchOrders } = useHarvestingOrdersQuery({
      startDate,
      endDate,
    });

  useEffect(() => {
    setComment(order.harvestingComments ?? '');
  }, [order.harvestingComments]);

  const handleDelete = async () => {
    await deleteWorkOrder({ id: order.id, orderId: order.orderId });
    refetchOrders();
  };

  const handleCommentsChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setComment(event.target.value);
  };

  const handleCommentsBlur = async () => {
    if (comment !== order.harvestingComments) {
      const args = {
        id: order.id,
        comment,
      };
      await updateWorkOrderComment(args);
    }
  };

  drag(drop(ref));

  return (
    <>
      <tr
        ref={ref}
        className={classNames(
          'border-gray-200',
          isOver && 'border-t-4 border-t-gray-500'
        )}
      >
        <td className={cellClassName}>
          <span className="font-semibold">{order.orderNumber}</span>
          {!!order.orderComments && (
            <div className="italic">{order.orderComments}</div>
          )}
        </td>
        <td className={cellClassName}>
          <span className="font-semibold">
            {order.plantSize}&nbsp;{order.plantCrop}&nbsp;{order.customer}
          </span>
          <div className="pl-4 italic">
            {order.varieties.map((variety) => (
              <div key={variety.name}>{variety.name}</div>
            ))}
          </div>
        </td>
        <td className={cellClassName}>
          <div className="flex flex-row">
            <div>
              Zone: {order.zone}
              <br />
              {order.finalRound ? 'Final Round' : 'Next Round: ' + order.nextRoundDate}
              <br />
              <div>Crew Size: {order.crewSize}</div>
            </div>
          </div>
        </td>
        <td className={classNames(cellClassName, 'text-right')}>
          {formatNumber(order.pots)} pots
          <br />
          {formatNumber(order.cases)} cases
        </td>
        <td className={classNames(cellClassName, 'text-right')}>
          {formatNumber(estimatedHours, '0,0.0')}
        </td>
        <td className={classNames(cellClassName, 'w-1 text-center')}>
          <div className="flex flex-row">
            <div
              className="btn-secondary h-8 w-8 cursor-pointer px-2 py-1 text-center"
              // @ts-ignore
              ref={drag}
            >
              <Icon icon="arrows-up-down" />
            </div>
            <button
              type="button"
              className="delete p-1 text-red-500"
              onClick={handleDelete}
            >
              <Icon icon="trash" />
            </button>
          </div>
        </td>
      </tr>
      <tr className="border-b border-gray-200">
        <td className={cellClassName} colSpan={6}>
          <label className="block text-xs italic">Schedule Notes</label>
          <input
            type="text"
            className="mb-2 w-full rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
            value={comment ?? ''}
            onChange={handleCommentsChange}
            onBlur={handleCommentsBlur}
          />
        </td>
      </tr>
    </>
  );
}
