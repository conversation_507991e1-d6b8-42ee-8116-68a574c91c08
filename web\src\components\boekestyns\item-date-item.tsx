import { useAppSelector } from '@/services/hooks';
import { formatDate, formatNumber } from '@/utils/format';
import { selectItems } from './boekestyn-list-slice';
import { ListItem } from './item-list-item';

interface DateItemProps {
  date: string;
  season: string | null;
}

export function DateItem({ date, season }: DateItemProps) {
  const items = useAppSelector(selectItems),
    dateItems = items.filter(
      (i) => i.requiredDate === date && i.season === season
    ),
    totalQuantity = dateItems.reduce((total, i) => total + i.caseCount, 0);

  return (
    <tbody className="odd:bg-gray-100 even:bg-white">
      <tr className="sticky top-[31px] border-b bg-gray-200">
        <th colSpan={3} className="text-gray-7000 px-1 py-2 text-left text-sm">
          {season || formatDate(date, 'EEEE MMM d')}
        </th>
        <th className="text-gray-7000 px-1 py-2 text-center text-sm">
          {formatNumber(totalQuantity)}
        </th>
        <th colSpan={8}>&nbsp;</th>
      </tr>
      {dateItems.map((item) => (
        <ListItem key={item.id} item={item} />
      ))}
    </tbody>
  );
}
