import { useSpacingQuery } from 'api/boekestyn-spacing-service';
import { SpacingOrders } from '@/components/boekestyns/admin/spacing-orders';
import { SpacingSchedules } from '@/components/boekestyns/admin/spacing-schedules';
import { setLastSelected } from '@/components/boekestyns/admin/admin-slice';
import { useAppDispatch } from '@/services/hooks';
import { routes } from '@/services/routes';
import { AdminLayout } from '../../../components/boekestyns/admin/layout';

export default function Spacing() {
  const dispatch = useAppDispatch();

  dispatch(setLastSelected(routes.boekestyns.admin.spacing.to()));

  useSpacingQuery();

  return (
    <div className="flex h-full flex-col overflow-y-auto bg-blue-500">
      <div className="m-4 flex flex-grow flex-col overflow-y-auto rounded bg-white">
        <div className="grid h-full grid-cols-2 gap-x-2 overflow-y-auto p-2">
          <div className="flex h-full flex-col overflow-y-auto rounded border">
            <SpacingOrders />
          </div>
          <div className="flex h-full flex-col overflow-y-auto rounded border">
            <SpacingSchedules />
          </div>
        </div>
      </div>
    </div>
  );
}

Spacing.getLayout = function getLayout(page: React.ReactElement) {
  return <AdminLayout>{page}</AdminLayout>;
};
