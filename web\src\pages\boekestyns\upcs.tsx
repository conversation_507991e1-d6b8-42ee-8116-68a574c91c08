import { useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import * as models from 'api/models/boekestyns';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import {
  getTaskList,
  selectTasks,
  selectTaskUrl,
  selectError,
  clearError,
  selectTask,
  completeUpc,
} from '@/components/boekestyns/tasks/task-slice';
import { classNames } from '@/utils/class-names';
import { isUpc } from '@/utils/equals';
import { formatDate } from '@/utils/format';

export default function Tasks() {
  const dispatch = useAppDispatch(),
    tasks = useAppSelector(selectTasks),
    taskUrl = useAppSelector(selectTaskUrl),
    error = useAppSelector(selectError);

  useEffect(() => {
    dispatch(getTaskList());
  }, [dispatch]);

  const handleTaskClick = (task: models.BoekestynTask) => {
    dispatch(selectTask(task));
  };

  const handleClearError = () => {
    dispatch(clearError());
  };

  const handleUpcComplete = (id: number) => {
    dispatch(completeUpc(id));
  };

  return (
    <>
      <Head>
        <title>Boekestyn UPCs</title>
      </Head>
      <header className="bg-white shadow">
        <div className="grid grid-cols-1">
          <div className="mb-4 border-b shadow">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-8">
              <div className="flex">
                <nav className="ml-24 flex flex-row">
                  <Link
                    href={routes.boekestyns.list.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Boekestyn Item List
                  </Link>
                  <Link
                    href={routes.boekestyns.sales.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Boekestyn Sales
                  </Link>
                  <Link
                    href={routes.boekestyns.sticking.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Sticking
                  </Link>
                  <Link
                    href={routes.boekestyns.spacing.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Spacing
                  </Link>
                  <Link
                    href={routes.boekestyns.spacing.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Harvesting
                  </Link>
                  <div className="group relative my-2 text-nowrap rounded-lg bg-blue-500 p-2 text-center text-sm font-medium text-white">
                    UPCs
                  </div>
                  <Link
                    href={routes.boekestyns.prep.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Order Prep
                  </Link>
                  <Link
                    href={routes.boekestyns.packing.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Order Packing
                  </Link>
                  <Link
                    href={routes.boekestyns.admin.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Admin
                  </Link>
                </nav>
              </div>
            </div>
          </div>
          <div className="bg-gray-100 py-2">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-8">
              <div className="flex flex-grow items-center align-top">
                <div className="flex w-full flex-col rounded p-2"></div>
              </div>
            </div>
          </div>
        </div>
      </header>
      <Error error={error} clear={handleClearError} />
      <main className="flex-grow overflow-auto">
        <div className="mx-auto h-full px-8">
          <div className="mt-8 flex h-full flex-col">
            <div className="-mx-8 -my-2 h-full">
              <div className="grid min-w-full grid-cols-1 px-8 py-2 align-middle lg:grid-cols-2">
                <div className="rounded-lg shadow ring-1 ring-black ring-opacity-5">
                  <table className="min-w-full divide-y divide-gray-300">
                    <thead>
                      <tr className="sticky top-0 z-10">
                        <th className="bg-gray-100 px-1 py-2 text-center text-xs font-semibold text-gray-900">
                          Qty
                        </th>
                        <th className="bg-gray-100 px-1 py-2 text-center text-xs font-semibold text-gray-900">
                          Description
                        </th>
                        <th className="whitespace-nowrap bg-gray-100 px-1 py-2 text-center text-xs font-semibold text-gray-900">
                          Box Code
                        </th>
                        <th className="bg-gray-100 px-1 py-2 text-center text-xs font-semibold text-gray-900">
                          Comment
                        </th>
                        <th className="w-1 bg-gray-100 px-1 py-2">&nbsp;</th>
                      </tr>
                    </thead>
                    {tasks
                      .filter((t) => t.items.some((i) => isUpc(i.comment)))
                      .map((task) => (
                        <tbody
                          key={task.id}
                          className="odd:bg-gray-100 even:bg-white"
                        >
                          <tr className="sticky top-[31px] border-b bg-gray-200">
                            <th
                              colSpan={5}
                              className="text-gray-7000 px-1 py-2 text-left text-sm"
                            >
                              <button
                                type="button"
                                onClick={() => handleTaskClick(task)}
                              >
                                {formatDate(task.requiredDate)}
                                &nbsp;&mdash;&nbsp;{task.purchaseOrderNumber}
                              </button>
                            </th>
                          </tr>
                          {task.items
                            .filter((i) => isUpc(i.comment))
                            .map((item) => (
                              <tr key={item.description} className="border-b">
                                <td
                                  className={classNames(
                                    item.upcComplete && 'bg-yellow-100'
                                  )}
                                >
                                  <div className={classNames('my-2 ml-2')}>
                                    {item.orderQty}
                                  </div>
                                </td>
                                <td
                                  className={classNames(
                                    item.upcComplete && 'bg-yellow-100'
                                  )}
                                >
                                  <div
                                    className={classNames(
                                      'my-2 whitespace-nowrap'
                                    )}
                                  >
                                    {item.description}
                                  </div>
                                </td>
                                <td
                                  className={classNames(
                                    item.upcComplete && 'bg-yellow-100'
                                  )}
                                >
                                  <div
                                    className={classNames('my-2 text-center')}
                                  >
                                    {item.boxCode}
                                  </div>
                                </td>
                                <td
                                  className={classNames(
                                    item.upcComplete && 'bg-yellow-100'
                                  )}
                                >
                                  <div className={classNames('my-2')}>
                                    {item.comment}
                                  </div>
                                </td>
                                <td
                                  className={classNames(
                                    item.upcComplete && 'bg-yellow-100'
                                  )}
                                >
                                  {!item.upcComplete && (
                                    <button
                                      type="button"
                                      className="small secondary m-2 rounded border px-2"
                                      onClick={() => handleUpcComplete(item.id)}
                                    >
                                      <Icon icon="check" />
                                    </button>
                                  )}
                                  {!!item.upcComplete && (
                                    <div className="text-center text-yellow-900">
                                      <Icon icon="check-circle" />
                                    </div>
                                  )}
                                </td>
                              </tr>
                            ))}
                        </tbody>
                      ))}
                  </table>
                </div>
                <div className="m-4 hidden h-full border lg:block">
                  {!!taskUrl && (
                    <iframe
                      title="Purchase Order"
                      src={taskUrl}
                      className="h-full w-full"
                    ></iframe>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  );
}
