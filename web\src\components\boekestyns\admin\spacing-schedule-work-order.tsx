import { useRef, useState, useEffect, useMemo } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import {
  useDeleteSpacingWorkOrderMutation,
  useSortSpacingWorkOrdersMutation,
  useUpdateSpacingWorkOrderCommentMutation,
  useSpacingOrdersQuery,
} from 'api/boekestyn-spacing-service';
import * as models from 'api/models/boekestyns';
import { Icon } from '@/components/icon';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { classNames } from '@/utils/class-names';
import { formatNumber } from '@/utils/format';
import { selectStartDate, selectEndDate } from './admin-slice';
import { moveItem, SortSpacingWorkOrderType } from './spacing-slice';

interface SpacingScheduleWorkOrderProps {
  scheduleId: number;
  order: models.SpacingWorkOrder;
}

export function SpacingScheduleWorkOrder({
  scheduleId,
  order,
}: SpacingScheduleWorkOrderProps) {
  const dispatch = useAppDispatch(),
    startDate = useAppSelector(selectStartDate),
    endDate = useAppSelector(selectEndDate),
    [comment, setComment] = useState(order.spacingComments ?? ''),
    [deleteWorkOrder] = useDeleteSpacingWorkOrderMutation(),
    [sortWorkOrders] = useSortSpacingWorkOrdersMutation(),
    [updateWorkOrderComment] = useUpdateSpacingWorkOrderCommentMutation(),
    estimatedHours = useMemo(
      () =>
        order.spacingPotsPerHour
          ? order.potsToSpace / order.spacingPotsPerHour
          : 0,
      [order]
    ),
    fromTables = useMemo(() => {
      const cuttings = order.potsToSpace * order.cuttingsPerPot,
        perTable =
          order.fromSpaceType === 'Tight'
            ? order.cuttingsPerTableTight
            : order.fromSpaceType === 'Partial'
            ? order.cuttingsPerTablePartiallySpaced || 0
            : order.cuttingsPerTableSpaced,
        tables = perTable ? cuttings / perTable : 0;
      return tables;
    }, [order]),
    toTables = useMemo(() => {
      const cuttings = order.potsToSpace * order.cuttingsPerPot,
        perTable =
          order.toSpaceType === 'Tight'
            ? order.cuttingsPerTableTight
            : order.toSpaceType === 'Partial'
            ? order.cuttingsPerTablePartiallySpaced || 0
            : order.cuttingsPerTableSpaced,
        tables = perTable ? cuttings / perTable : 0;
      return tables;
    }, [order]),
    ref = useRef<HTMLTableRowElement>(null),
    cellClassName = 'whitespace-nowrap px-2 py-1 text-gray-700 align-top',
    [, drag] = useDrag(() => ({
      type: `${SortSpacingWorkOrderType}-${scheduleId}`,
      item: order,
    })),
    [{ isOver }, drop] = useDrop<
      models.SpacingWorkOrder,
      void,
      { isOver: boolean }
    >(() => ({
      accept: `${SortSpacingWorkOrderType}-${scheduleId}`,
      collect: (monitor) => ({
        isOver: monitor.isOver(),
      }),
      drop(droppedItem) {
        dispatch(
          moveItem({ scheduleId, existingItem: order, movingItem: droppedItem })
        ).then(({ payload }) => {
          const workOrders = payload as models.SpacingWorkOrder[] | undefined;
          if (workOrders) {
            const args = {
              workOrders: workOrders.map((wo) => ({
                workOrderId: wo.id,
                sortOrder: wo.sortOrder,
              })),
            };
            sortWorkOrders(args);
          }
        });
      },
    })),
    { refetch: refetchOrders } = useSpacingOrdersQuery({
      startDate,
      endDate,
    });

  useEffect(() => {
    setComment(order.spacingComments ?? '');
  }, [order.spacingComments]);

  const handleDelete = async () => {
    await deleteWorkOrder({ id: order.id, orderId: order.orderId });
    refetchOrders();
  };

  const handleCommentsChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setComment(event.target.value);
  };

  const handleCommentsBlur = async () => {
    if (comment !== order.spacingComments) {
      const args = {
        id: order.id,
        comment,
      };
      await updateWorkOrderComment(args);
    }
  };

  drag(drop(ref));

  return (
    <>
      <tr
        ref={ref}
        className={classNames(
          'border-gray-200',
          isOver && 'border-t-4 border-t-gray-500'
        )}
      >
        <td className={cellClassName}>
          <span className="font-semibold">{order.orderNumber}</span>
          {!!order.orderComments && (
            <div className="italic">{order.orderComments}</div>
          )}
        </td>
        <td className={cellClassName}>
          <span className="font-semibold">
            {order.plantSize}&nbsp;{order.plantCrop}&nbsp;{order.customer}
          </span>
          <div className="pl-4 italic">
            {order.varieties.map((variety) => (
              <div key={variety.name}>{variety.name}</div>
            ))}
          </div>
        </td>
        <td className={cellClassName}>
          <div className="flex flex-row">
            <div className="text-right">
              {order.fromSpaceType}
              <br />
              {formatNumber(fromTables, '0,0.00')} tables
            </div>
            <Icon icon="arrow-right" className="mx-1" />
            <div>
              {order.toSpaceType}
              <br />
              {formatNumber(toTables, '0,0.00')} tables
            </div>
          </div>
        </td>
        <td className={classNames(cellClassName, 'text-center')}>
          {order.requiresPinching && <Icon icon="check-square" />}
        </td>
        <td className={classNames(cellClassName, 'text-right')}>
          {formatNumber(order.potsToSpace)}
        </td>
        <td className={classNames(cellClassName, 'text-right')}>
          {formatNumber(estimatedHours, '0,0.0')}
        </td>
        <td className={classNames(cellClassName, 'w-1 text-center')}>
          <div className="flex flex-row">
            <div
              className="btn-secondary h-8 w-8 cursor-pointer px-2 py-1 text-center"
              // @ts-ignore
              ref={drag}
            >
              <Icon icon="arrows-up-down" />
            </div>
            <button
              type="button"
              className="delete p-1 text-red-500"
              onClick={handleDelete}
            >
              <Icon icon="trash" />
            </button>
          </div>
        </td>
      </tr>
      <tr className="border-b border-gray-200">
        <td className={cellClassName} colSpan={8}>
          <label className="block text-xs italic">Schedule Notes</label>
          <input
            type="text"
            className="mb-2 w-full rounded-md border-gray-300 text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
            value={comment ?? ''}
            onChange={handleCommentsChange}
            onBlur={handleCommentsBlur}
          />
        </td>
      </tr>
    </>
  );
}
