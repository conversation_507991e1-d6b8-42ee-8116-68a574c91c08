export interface CustomerItemCodeDefault {
  customerId: number;
  spireInventoryId: number;
  shipToId: number | null;
  customerItemCode: string;
}

export interface ProductDefault {
  spireInventoryId: number;
  boekestynPlantId: string | null;
  boekestynCustomerAbbreviation: string | null;
  upgradeLabourHours: number | null;
  quantityPerFinishedItem: number | null;
  isUpgrade: boolean;
  ignoreOverrideQuantity: boolean;

  products: ProductDefaultBoekestynProduct[];
  overrides: ProductDefaultBoekestynProductOverride[];
}

export interface ProductDefaultBoekestynProduct {
  id: number;
  spireInventoryId: number;
  boekestynPlantId: string;
  boekestynCustomerAbbreviation: string | null;
  quantityPerFinishedItem: number;
}

export interface ProductDefaultBoekestynProductOverride {
  id: number;
  spireInventoryId: number;
  startWeek: number;
  endWeek: number;
  boekestynPlantId: string;
  boekestynCustomerAbbreviation: string | null;
  quantityPerFinishedItem: number;
}

export interface UpgradeOption {
  id: number;
  containerPickDescription: string | null;
  origins: string | null;
  costs: string | null;
}

export interface SpireRefreshItem {
  prebooksRefreshed: number;
  futureOrdersRefreshed: number;
}

export interface PriceDeviationWarning {
  id: number;
  maxUnitPrice: number | null;
  minPackSize: number | null;
  allowableDeviation: number;
  customerId: number | null;
  shipToId: number | null;
}

export interface DefaultVendorOverride {
  id: number;
  spirePartNumbers: string[];
  items: DefaultVendorOverrideItem[];
}

export interface DefaultVendorOverrideItem {
  id: number;
  defaultVendorOverrideId: number;
  startWeek: number;
  endWeek: number;
  vendorId: number;
}

export interface ShipToDefault {
  id: number;
  shipToId: number;
  defaultFreightPerCase: number | null;
}
