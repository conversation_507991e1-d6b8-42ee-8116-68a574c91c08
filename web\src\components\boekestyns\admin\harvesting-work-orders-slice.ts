import { createSlice, PayloadAction, createSelector } from '@reduxjs/toolkit';
import { RootState } from '@/services/store';
import * as models from 'api/models/boekestyns';
import { DateTime } from 'luxon';

interface WorkOrderData {
  crewSize: number;
  finalRound: boolean;
  harvestingComments: string | null;
  varieties: models.HarvestingWorkOrderVariety[];
  maximumHarvestRounds: number;
  defaultExpectedHarvestPercentage: number | null;
}

interface HarvestingWorkOrdersState {
  // Current order being scheduled
  selectedOrder: models.HarvestingOrder | null;

  // Work orders data by date
  workOrdersByDate: { [date: string]: WorkOrderData };

  // UI state
  selectedTab: number;
  showDatePicker: boolean;
  workOrderDate: string;

  // Variety selection state
  selectedVarieties: { [varietyName: string]: boolean } | null;
  //varietyExpectedPercentages: { [varietyName: string]: number };
}

const initialWorkOrderData: WorkOrderData = {
  crewSize: 1,
  finalRound: false,
  harvestingComments: null,
  varieties: [],
  maximumHarvestRounds: 1,
  defaultExpectedHarvestPercentage: null,
};

const initialState: HarvestingWorkOrdersState = {
  selectedOrder: null,
  workOrdersByDate: {
    [DateTime.now().toFormat('yyyy-MM-dd')]: { ...initialWorkOrderData }
  },
  selectedTab: 0,
  showDatePicker: false,
  workOrderDate: DateTime.now().toFormat('yyyy-MM-dd'),
  selectedVarieties: null,
  //varietyExpectedPercentages: {},
};

const harvestingWorkOrdersSlice = createSlice({
  name: 'harvestingWorkOrders',
  initialState,
  reducers: {
    // Order selection
    setSelectedOrder(state, action: PayloadAction<models.HarvestingOrder | null>) {
      state.selectedOrder = action.payload;
      if (action.payload) {
        // Initialize selected varieties based on order varieties
        state.selectedVarieties = action.payload.varieties.reduce(
          (acc, v) => ({ ...acc, [v.name]: true }),
          {}
        );
      } else {
        state.selectedVarieties = null;
      }
    },

    // Work order data management
    updateWorkOrderData(state, action: PayloadAction<{ date: string; updates: Partial<WorkOrderData> }>) {
      const { date, updates } = action.payload;
      if (state.workOrdersByDate[date]) {
        state.workOrdersByDate[date] = {
          ...state.workOrdersByDate[date],
          ...updates,
        };
      }
    },

    // Date management
    addWorkOrderDate(state, action: PayloadAction<string>) {
      const date = action.payload;
      if (!state.workOrdersByDate[date]) {
        state.workOrdersByDate[date] = { ...initialWorkOrderData };
      }
      state.showDatePicker = false;
      const dates = Object.keys(state.workOrdersByDate).sort();
      state.selectedTab = dates.indexOf(date);
    },

    removeWorkOrderDate(state, action: PayloadAction<string>) {
      const date = action.payload;
      const dates = Object.keys(state.workOrdersByDate);

      // Don't allow removing the last date
      if (dates.length > 1) {
        delete state.workOrdersByDate[date];

        // Adjust selected tab if necessary
        const remainingDates = Object.keys(state.workOrdersByDate).sort();
        if (state.selectedTab >= remainingDates.length) {
          state.selectedTab = remainingDates.length - 1;
        }
      }
    },

    // UI state management
    setSelectedTab(state, action: PayloadAction<number>) {
      state.selectedTab = action.payload;
    },

    setShowDatePicker(state, action: PayloadAction<boolean>) {
      state.showDatePicker = action.payload;
    },

    setWorkOrderDate(state, action: PayloadAction<string>) {
      state.workOrderDate = action.payload;
    },

    // Variety management
    setSelectedVarieties(state, action: PayloadAction<{ [varietyName: string]: boolean } | null>) {
      state.selectedVarieties = action.payload;
    },

    updateVarietySelection(state, action: PayloadAction<{ varietyName: string; selected: boolean }>) {
      const { varietyName, selected } = action.payload;
      if (state.selectedVarieties) {
        state.selectedVarieties[varietyName] = selected;
      }
    },

    setVarietyExpectedPercentages(state, action: PayloadAction<{ date: string; varietyName: string; percentage: number }[]>) {
      const updates = action.payload;
      updates.forEach(update => {
        const { date, varietyName, percentage } = update;
        if (state.workOrdersByDate[date]) {
          state.workOrdersByDate[date].varieties = state.workOrdersByDate[date].varieties.map(v =>
            v.name === varietyName ? { ...v, expectedHarvestPercentage: percentage } : v
          );
        }
      });
    },

    updateVarietyExpectedPercentage(state, action: PayloadAction<{ date:string; varietyName: string; percentage: number }>) {
      const { date, varietyName, percentage } = action.payload;
      if (state.workOrdersByDate[date]) {
        state.workOrdersByDate[date].varieties = state.workOrdersByDate[date].varieties.map(v =>
          v.name === varietyName ? { ...v, expectedHarvestPercentage: percentage } : v
        );
      }
    },

    // Reset state
    resetWorkOrders(state) {
      return {
        ...initialState,
        workOrdersByDate: {
          [DateTime.now().toFormat('yyyy-MM-dd')]: { ...initialWorkOrderData }
        },
      };
    },
  },
});

export const {
  setSelectedOrder,
  updateWorkOrderData,
  addWorkOrderDate,
  removeWorkOrderDate,
  setSelectedTab,
  setShowDatePicker,
  setWorkOrderDate,
  setSelectedVarieties,
  updateVarietySelection,
  setVarietyExpectedPercentages,
  updateVarietyExpectedPercentage,
  resetWorkOrders,
} = harvestingWorkOrdersSlice.actions;

// Selectors
export const selectSelectedOrder = (state: RootState) =>
  state.harvestingWorkOrders.selectedOrder;

export const selectWorkOrdersByDate = (state: RootState) =>
  state.harvestingWorkOrders.workOrdersByDate;

export const selectSelectedTab = (state: RootState) =>
  state.harvestingWorkOrders.selectedTab;

export const selectShowDatePicker = (state: RootState) =>
  state.harvestingWorkOrders.showDatePicker;

export const selectWorkOrderDate = (state: RootState) =>
  state.harvestingWorkOrders.workOrderDate;

export const selectSelectedVarieties = (state: RootState) =>
  state.harvestingWorkOrders.selectedVarieties;

// Computed selectors
export const selectWorkOrderDates = createSelector(
  [selectWorkOrdersByDate],
  (workOrdersByDate) => Object.keys(workOrdersByDate).sort()
);

export const selectWorkOrderForDate = createSelector(
  [selectWorkOrdersByDate, (state: RootState, date: string) => date],
  (workOrdersByDate, date) => workOrdersByDate[date] || initialWorkOrderData
);

export const selectCurrentWorkOrder = createSelector(
  [selectWorkOrdersByDate, selectWorkOrderDates, selectSelectedTab],
  (workOrdersByDate, dates, selectedTab) => {
    const currentDate = dates[selectedTab];
    return currentDate ? workOrdersByDate[currentDate] : initialWorkOrderData;
  }
);

export const selectWorkOrderVarieties = createSelector(
  [selectCurrentWorkOrder],
  (workOrder) => workOrder.varieties || []
);

export const selectVarietyExpectedPercentages = createSelector(
  [selectCurrentWorkOrder],
  (workOrder) => {
    return workOrder.varieties?.reduce(
      (acc, v) => ({ ...acc, [v.name]: v.expectedHarvestPercentage || 0 }),
      {}
    ) || {};
  }
);

export const selectCurrentDate = createSelector(
  [selectWorkOrderDates, selectSelectedTab],
  (dates, selectedTab) => dates[selectedTab] || DateTime.now().toFormat('yyyy-MM-dd')
);

export default harvestingWorkOrdersSlice.reducer;