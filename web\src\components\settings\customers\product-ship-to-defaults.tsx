import { useAppSelector, useAppDispatch } from '@/services/hooks';
import * as spire from 'api/models/spire';
import {
  selectProductShipToDefaults,
  selectCustomerItemCodeByShipTo,
  selectQuery,
  setQuery,
} from './customer-settings-slice';
import { ProductShipToDefault } from './product-ship-to-default';

interface ProductShipToDefaultsProps {
  shipTo: spire.CustomerShipTo;
}

export function ProductShipToDefaults({ shipTo }: ProductShipToDefaultsProps) {
  const dispatch = useAppDispatch(),
    allProductShipToDefaults = useAppSelector(selectProductShipToDefaults),
    customerItemCodeByShipTo = useAppSelector(selectCustomerItemCodeByShipTo),
    query = useAppSelector(selectQuery),
    productShipToDefaults = allProductShipToDefaults.filter(
      (d) => d.shipToId === shipTo.id
    );

  const handleQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setQuery(e.target.value));
  };

  return (
    <div className="flex h-full flex-col">
      <div className="mb-2 flex items-center gap-4">
        <h2 className="p-2 text-lg font-bold text-blue-500">{shipTo.shipId}</h2>
        <label htmlFor="item-search" className="text-sm">
          Search for Products
        </label>
        <input
          id="item-search"
          type="search"
          value={query}
          className="block text-xs"
          onChange={handleQueryChange}
        />
      </div>
      <div className="flex flex-grow overflow-y-auto">
        <table className="min-w-full divide-y divide-gray-300">
          <thead>
            <tr className="sticky top-0 z-10">
              <th className="bg-gray-100 px-2 py-3.5 pl-3 text-left font-semibold text-gray-900">
                Product
              </th>
              <th className="bg-gray-100 px-2 py-3.5 pl-3 text-center font-semibold text-gray-900">
                Pot Cover
              </th>
              <th className="bg-gray-100 px-2 py-3.5 pl-3 text-center font-semibold text-gray-900">
                UPC
              </th>
              <th className="bg-gray-100 px-2 py-3.5 pl-3 text-center font-semibold text-gray-900">
                W&M
              </th>
              <th className="bg-gray-100 px-2 py-3.5 pl-3 text-center font-semibold text-gray-900">
                Retail
              </th>
              <th className="bg-gray-100 px-2 py-3.5 pl-3 text-center font-semibold text-gray-900">
                Price
              </th>
              {customerItemCodeByShipTo && (
                <th className="bg-gray-100 px-2 py-3.5 pl-3 text-center font-semibold text-gray-900">
                  Customer Item Code
                </th>
              )}
              <th className="bg-gray-100 px-2 py-3.5 pl-3 text-center font-semibold text-gray-900">
                &nbsp;
              </th>
            </tr>
          </thead>
          <tbody>
            {productShipToDefaults.map((d) => (
              <ProductShipToDefault key={d.id} productShipToDefault={d} />
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
