import {
  AsyncThunk,
  createAction,
  createAsyncThunk,
  createSelector,
  createSlice,
  PayloadAction,
} from '@reduxjs/toolkit';
import {
  spireApi,
  SpireCustomerDetailResponse,
  spireService,
} from 'api/spire-service';
import { prebookListApi, seasonsApi } from 'api/prebooks-service';
import { settingsApi } from 'api/settings-service';
import * as prebooks from 'api/models/prebooks';
import * as settings from 'api/models/settings';
import * as spire from 'api/models/spire';
import * as models from 'api/models/future-orders';
import * as holidays from 'api/models/holidays';
import { RootState } from '@/services/store';
import { noPotCover, potCovers as defaultPotCovers } from '../pot-covers';
import { createProblemDetails, ProblemDetails } from '@/utils/problem-details';
import {
  FutureOrderCreateResponse,
  FutureOrderDetailResponse,
  futureOrdersApi,
  futureOrdersListApi,
} from 'api/future-orders-service';
import { equals } from '@/utils/equals';
import { sortBy } from '@/utils/sort';
import { getBoekestynProducts } from '../boekestyns/sales/boekestyn-sales-functions';
import { findDefaultVendorOverride } from './item-functions';

const sortBySortOrder = sortBy('sortOrder');

export interface CreateItem extends models.FutureOrderCreateItem {
  expanded?: boolean;
}

interface FutureOrderCreateState {
  requiredDate: string | null;
  arrivalDate: string | null;
  seasonName: string | null;
  salesperson: spire.Salesperson | null;
  shipTo: spire.CustomerShipTo | null;
  customer: spire.Customer | null;
  shipVia: spire.ShippingMethod | null;
  boxCode: string | null;
  requiresLabels: boolean;
  customerPurchaseOrderNumber: string | null;
  freightPerCase: number | null;
  freightPerLoad: number | null;
  freightIsActual: boolean;
  spireNotes: string | null;
  growerItemNotes: string | null;
  comments: models.FutureOrderCreateComment[];
  internalComments: string | null;
  customerInfo: string | null;
  items: CreateItem[];
  productDefaults: settings.ProductDefault[];
  blanketItems: prebooks.PrebookBlanketItem[];
  seasons: prebooks.Season[];
  customerDetail: spire.CustomerDetail | null;
  salespeople: spire.Salesperson[];
  vendors: spire.Vendor[];
  customerItemCodeDefaults: settings.CustomerItemCodeDefault[];
  customerPotCovers: string[];
  availabilityUnitPrices: { [index: number]: number };
  defaultVendorOverrides: settings.DefaultVendorOverride[];
  holidays: holidays.Holiday[];
  showInventoryDialog: boolean;
  isLoading: boolean;
  error: ProblemDetails | null;
}

const initialState: FutureOrderCreateState = {
  requiredDate: null,
  arrivalDate: null,
  seasonName: null,
  customer: null,
  shipTo: null,
  salesperson: null,
  shipVia: null,
  boxCode: null,
  requiresLabels: false,
  customerPurchaseOrderNumber: null,
  freightPerCase: null,
  freightPerLoad: null,
  freightIsActual: false,
  spireNotes: null,
  growerItemNotes: null,
  comments: [],
  internalComments: null,
  customerInfo: null,
  items: [],
  productDefaults: [],
  blanketItems: [],
  seasons: [],
  customerDetail: null,
  salespeople: [],
  vendors: [],
  customerItemCodeDefaults: [],
  customerPotCovers: [],
  availabilityUnitPrices: {},
  defaultVendorOverrides: [],
  holidays: [],
  showInventoryDialog: false,
  isLoading: false,
  error: null,
};

export const getCustomerDetail: AsyncThunk<
  SpireCustomerDetailResponse | null,
  number | null,
  { state: RootState }
> = createAsyncThunk(
  'future-order-getCustomerDetail',
  async (id, { rejectWithValue, dispatch }) => {
    try {
      return id ? await spireService.customerDetail(id) : null;
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const createFutureOrder: AsyncThunk<
  FutureOrderCreateResponse,
  void,
  { state: RootState }
> = createAsyncThunk(
  'future-order-createFutureOrder',
  async (_, { rejectWithValue, getState }) => {
    try {
      const rootState = getState() as RootState,
        state = rootState.futureOrderCreate,
        {
          requiredDate,
          arrivalDate,
          seasonName,
          salesperson,
          shipTo,
          customer,
          shipVia,
          boxCode,
          requiresLabels,
          customerPurchaseOrderNumber,
          freightPerCase,
          freightPerLoad,
          freightIsActual,
          spireNotes,
          growerItemNotes,
          internalComments,
        } = state,
        items = state.items
          .map((i) => ({ ...i }))
          .sort(sortBySortOrder)
          .map((i, index) => ({ ...i, sortOrder: index + 1 })),
        nextCommentId =
          state.comments.reduce((min, c) => Math.min(min, c.id), 0) - 1,
        comments = state.comments
          .map((c) => ({ ...c }))
          .concat(
            internalComments
              ? [
                  {
                    id: nextCommentId,
                    comments: internalComments,
                    isStandardComment: false,
                  },
                ]
              : []
          ),
        futureOrder = {
          requiredDate,
          arrivalDate,
          seasonName,
          salespersonId: salesperson?.id || null,
          salespersonName: salesperson?.name || null,
          shipToId: shipTo?.id || null,
          shipToName: shipTo?.shipId || null,
          customerId: customer?.id || null,
          customerName: customer?.name || null,
          shipViaId: shipVia?.id || null,
          shipViaName: shipVia?.description || null,
          boxCode,
          requiresLabels,
          customerPurchaseOrderNumber,
          freightPerCase,
          freightPerLoad,
          freightIsActual,
          spireNotes,
          growerItemNotes,
          comments,
          items,
        };

      return await futureOrdersApi.create(futureOrder);
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

export const copyFutureOrder: AsyncThunk<
  FutureOrderDetailResponse,
  number,
  { state: RootState }
> = createAsyncThunk(
  'future-order-copyFutureOrder',
  async (id, { rejectWithValue, dispatch }) => {
    try {
      const detail = await futureOrdersApi.detail(id);
      if (detail.futureOrder.customerId) {
        dispatch<any>(getCustomerDetail(detail.futureOrder.customerId));
      }
      return detail;
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

interface SetItemArgs<T> {
  itemId: number;
  value: T;
}

export interface MoveItemArgs {
  existingItem: CreateItem;
  movingItem: CreateItem;
}

export interface InventoryItemWithDefaults
  extends spire.InventoryItem,
    Partial<prebooks.ProductShipToDefault> {
  id: number;
  blanketItemId: number | null;
  comments: string | null;
  vendorId: number | null;
  vendorName: string | null;
  phytoRequired: boolean;
  boekestynPlantId: string | null;
  boekestynCustomerAbbreviation: string | null;
  useAvailabilityPricing: boolean;
  upgradeLabourHours?: number;
  quantityPerFinishedItem?: number | null;
  upgradeSheet?: boolean;
  specialPrice?: number | null;
  growerItemNotes?: string | null;
}

const getCustomerDetailFulfilled =
    createAction<SpireCustomerDetailResponse | null>(
      getCustomerDetail.fulfilled.type
    ),
  getCustomerDetailRejected = createAction<ProblemDetails>(
    getCustomerDetail.rejected.type
  ),
  createFutureOrderRejected = createAction<ProblemDetails>(
    createFutureOrder.rejected.type
  ),
  copyFutureOrderPending = createAction(copyFutureOrder.pending.type),
  copyFutureOrderFulfilled = createAction<FutureOrderDetailResponse>(
    copyFutureOrder.fulfilled.type
  ),
  copyFutureOrderRejected = createAction<ProblemDetails>(
    copyFutureOrder.rejected.type
  );

const futureOrderCreateSlice = createSlice({
  name: 'future-order-create',
  initialState,
  reducers: {
    clearState(state) {
      Object.assign(state, { ...initialState });
    },
    clearError(state) {
      state.error = null;
    },
    setError(state, { payload }: PayloadAction<ProblemDetails | string>) {
      const error =
        typeof payload === 'string' ? createProblemDetails(payload) : payload;
      state.error = error;
    },
    setCustomer(state, { payload }: PayloadAction<spire.Customer | null>) {
      state.customer = payload;
    },
    setCustomerDetail(state, { payload }: PayloadAction<spire.CustomerDetail>) {
      state.customerDetail = payload;
      state.customer = {
        id: payload.id,
        customerNo: payload.customerNo,
        name: payload.name,
        defaultShipTo: payload.defaultShipTo,
      };
    },
    setShipTo(state, { payload }: PayloadAction<spire.CustomerShipTo | null>) {
      state.shipTo = payload;

      const salesperson =
          state.salespeople.find(
            (s) => s.code === payload?.salesperson?.code
          ) || null,
        boxCode = payload?.boxCode || null,
        labels = payload?.labels || null;

      state.salesperson = salesperson;
      state.boxCode = boxCode;
      state.requiresLabels = equals(models.SpecialLabels, labels);
    },
    setSalesperson(
      state,
      { payload }: PayloadAction<spire.Salesperson | null>
    ) {
      state.salesperson = payload;
    },
    setRequiredDate(state, { payload }: PayloadAction<string | null>) {
      state.requiredDate = payload;

      const items = state.items.map((i) => ({ ...i })),
        season = state.seasons.find((s) => s.name === state.seasonName) || null;
      items.forEach((i) => {
        const productDefault = state.productDefaults.find(
            (d) => d.spireInventoryId === i.spireInventoryId
          ),
          boekestynProducts = getBoekestynProducts(
            i.vendorId,
            payload,
            season,
            productDefault
          );

        i.boekestynProducts = boekestynProducts;
      });
      state.items = items;
    },
    setArrivalDate(state, { payload }: PayloadAction<string | null>) {
      state.arrivalDate = payload;
    },
    setSeasonName(state, { payload }: PayloadAction<string | null>) {
      state.seasonName = payload;
    },
    setShipVia(state, { payload }: PayloadAction<spire.ShippingMethod | null>) {
      state.shipVia = payload;
    },
    setBoxCode(state, { payload }: PayloadAction<string | null>) {
      state.boxCode = payload;
    },
    setRequiresLabels(state, { payload }: PayloadAction<boolean>) {
      state.requiresLabels = payload;
    },
    setCustomerPurchaseOrderNumber(
      state,
      { payload }: PayloadAction<string | null>
    ) {
      state.customerPurchaseOrderNumber = payload;
    },
    setFreightPerCase(state, { payload }: PayloadAction<number | null>) {
      state.freightPerCase = payload;
    },
    setFreightPerLoad(state, { payload }: PayloadAction<number | null>) {
      state.freightPerLoad = payload;
    },
    setFreightIsActual(state, { payload }: PayloadAction<boolean>) {
      state.freightIsActual = payload;
    },
    setComments(
      state,
      { payload }: PayloadAction<models.FutureOrderCreateComment[]>
    ) {
      state.comments = payload;
    },
    setInternalComments(state, { payload }: PayloadAction<string | null>) {
      state.internalComments = payload;
    },
    setGrowerItemNotes(state, { payload }: PayloadAction<string | null>) {
      state.growerItemNotes = payload;
    },
    setSpireNotes(state, { payload }: PayloadAction<string | null>) {
      state.spireNotes = payload;
    },
    setCustomerInfo(state, { payload }: PayloadAction<string | null>) {
      state.customerInfo = payload;
    },
    addItem(state, { payload }: PayloadAction<InventoryItemWithDefaults>) {
      const id = state.items.reduce((min, i) => Math.min(min, i.id), 0) - 1,
        sortOrder =
          state.items.reduce((max, i) => Math.max(max, i.sortOrder), 0) + 1,
        {
          dateCode,
          hasPotCover,
          potCover,
          upc,
          weightsAndMeasures,
          retail,
          blanketItemId,
          comments,
          unitPrice,
          customerItemCode,
          boekestynPlantId,
          boekestynCustomerAbbreviation,
          useAvailabilityPricing,
          upgradeLabourHours: payloadUpgradeLabourHours,
          quantityPerFinishedItem: payloadQuantityPerFinishedItem,
          upgradeSheet,
          specialPrice,
          growerItemNotes,
        } = payload,
        productDefault = state.productDefaults.find(
          (pd) => pd.spireInventoryId === payload.id
        ),
        upgradeLabourHours =
          payloadUpgradeLabourHours || productDefault?.upgradeLabourHours || 0,
        quantityPerFinishedItem =
          payloadQuantityPerFinishedItem === undefined
            ? productDefault?.quantityPerFinishedItem || null
            : payloadQuantityPerFinishedItem,
        phytoRequired = equals(payload.uom?.location, spire.PhytoLocation),
        season = state.seasons.find((s) => s.name === state.seasonName) || null,
        boekestynProducts = getBoekestynProducts(
          payload.vendorId,
          state.requiredDate,
          season,
          productDefault
        );

      const item: CreateItem = {
        id,
        sortOrder,
        vendorId: payload.vendorId || null,
        vendorName: payload?.vendorName || null,
        spireInventoryId: payload.id,
        spirePartNumber: payload.partNo,
        description: payload.description,
        hasPotCover: !!hasPotCover,
        potCover: potCover || null,
        dateCode: dateCode || null,
        upc: upc || null,
        weightsAndMeasures: !!weightsAndMeasures,
        retail: retail || null,
        orderQuantity: 0,
        isApproximate: false,
        createPrebook: true,
        blanketItemId,
        isBlanket: false,
        comments,
        unitPrice: unitPrice || null,
        useAvailabilityPricing: !!useAvailabilityPricing,
        customerItemCode: customerItemCode || null,
        upgradeSheet: !!upgradeSheet,
        phytoRequired,
        boekestynPlantId,
        boekestynCustomerAbbreviation,
        upgradeLabourHours,
        quantityPerFinishedItem,
        specialPrice: specialPrice || null,
        growerItemNotes: growerItemNotes || null,
        expanded: true,
        boekestynProducts,
      };

      if (payload.blanketItemId) {
        const blanketItem = state.blanketItems.find(
          (i) => i.id === payload.blanketItemId
        );
        if (blanketItem) {
          item.vendorId = blanketItem.vendorId;
          item.vendorName = blanketItem.vendorName;
        }
      } else {
        const override = findDefaultVendorOverride(
            item.spirePartNumber,
            state.requiredDate,
            state.defaultVendorOverrides
          ),
          overrideVendor = state.vendors.find(
            (v) => v.id === override?.vendorId
          );
        if (overrideVendor) {
          item.vendorId = overrideVendor.id;
          item.vendorName = overrideVendor.name;
        }
      }

      if (!item.vendorId && payload.primaryVendor?.vendorNo) {
        const primaryVendor = state.vendors.find(
          (v) => v.vendorNo === payload.primaryVendor?.vendorNo
        );
        if (primaryVendor) {
          item.vendorId = primaryVendor.id;
          item.vendorName = primaryVendor.name;
        }
      }

      const items = state.items.map((i) => ({ ...i })).concat([item]);

      state.items = items;
    },
    duplicateItem(state, { payload }: PayloadAction<number>) {
      const id = state.items.reduce((min, i) => Math.min(min, i.id), 0) - 1,
        sortOrder =
          state.items.reduce((max, i) => Math.max(max, i.sortOrder), 0) + 1,
        existing = state.items.find((i) => i.id === payload);

      if (existing) {
        const item: CreateItem = {
          id,
          sortOrder,
          vendorId: existing.vendorId,
          vendorName: existing.vendorName,
          spireInventoryId: existing.spireInventoryId,
          spirePartNumber: existing.spirePartNumber,
          description: existing.description,
          hasPotCover: existing.hasPotCover,
          potCover: existing.potCover,
          dateCode: existing.dateCode,
          upc: existing.upc,
          weightsAndMeasures: existing.weightsAndMeasures,
          retail: existing.retail,
          orderQuantity: 0,
          isApproximate: existing.isApproximate,
          createPrebook: true,
          blanketItemId: existing.blanketItemId,
          isBlanket: false,
          comments: existing.comments,
          unitPrice: existing.unitPrice,
          useAvailabilityPricing: existing.useAvailabilityPricing,
          customerItemCode: existing.customerItemCode,
          upgradeSheet: existing.upgradeSheet,
          phytoRequired: existing.phytoRequired,
          boekestynPlantId: existing.boekestynPlantId,
          boekestynCustomerAbbreviation: existing.boekestynCustomerAbbreviation,
          upgradeLabourHours: existing.upgradeLabourHours,
          quantityPerFinishedItem: existing.quantityPerFinishedItem,
          specialPrice: existing.specialPrice,
          growerItemNotes: existing.growerItemNotes,
          boekestynProducts: existing.boekestynProducts,
          expanded: true,
        };

        const items = state.items.map((i) => ({ ...i })).concat([item]);

        state.items = items;
      }
    },
    removeItem(state, { payload }: PayloadAction<number>) {
      const items = state.items
        .map((i) => ({ ...i }))
        .filter((i) => i.id !== payload);

      items.forEach((i, index) => (i.sortOrder = index + 1));

      state.items = items;
    },
    setItemExpanded(state, { payload }: PayloadAction<SetItemArgs<boolean>>) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.expanded = value;
      }

      state.items = items;
    },
    setAllExpanded(state, { payload }: PayloadAction<boolean>) {
      const items = state.items.map((i) => ({
        ...i,
        expanded: payload,
      }));

      state.items = items;
    },
    setItems(
      state,
      { payload }: PayloadAction<models.FutureOrderCreateItem[]>
    ) {
      state.items = payload;
    },
    setShowInventoryDialog(state, { payload }: PayloadAction<boolean>) {
      state.showInventoryDialog = payload;
    },
    setItemInventoryItem(
      state,
      { payload }: PayloadAction<SetItemArgs<spire.InventoryItem | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.spireInventoryId = value?.id || null;
        item.spirePartNumber = value?.partNo || null;
        item.description = value?.description || null;

        if (item.spireInventoryId) {
          const defaults = state.customerItemCodeDefaults.find(
            (d) =>
              d.spireInventoryId === item.spireInventoryId &&
              d.customerId === state.customer?.id &&
              (!d.shipToId || d.shipToId === state.shipTo?.id)
          );
          if (defaults?.customerItemCode) {
            item.customerItemCode = defaults.customerItemCode;
          }
        } else {
          item.customerItemCode = null;
        }
      }

      state.items = items;
    },
    setItemHasPotCover(
      state,
      { payload }: PayloadAction<SetItemArgs<boolean>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.hasPotCover = value;
      }

      state.items = items;
    },
    setItemPotCover(
      state,
      { payload }: PayloadAction<SetItemArgs<string | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.potCover = value;
      }

      state.items = items;
    },
    setItemDateCode(
      state,
      { payload }: PayloadAction<SetItemArgs<string | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.dateCode = value;
      }

      state.items = items;
    },
    setItemUPC(state, { payload }: PayloadAction<SetItemArgs<string | null>>) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.upc = value;
      }

      state.items = items;
    },
    setItemWeightsAndMeasures(
      state,
      { payload }: PayloadAction<SetItemArgs<boolean>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.weightsAndMeasures = value;
      }

      state.items = items;
    },
    setItemRetail(
      state,
      { payload }: PayloadAction<SetItemArgs<string | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.retail = value;
      }

      state.items = items;
    },
    setItemComments(
      state,
      { payload }: PayloadAction<SetItemArgs<string | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.comments = value;
      }

      state.items = items;
    },
    setItemOrderQuantity(
      state,
      { payload }: PayloadAction<SetItemArgs<number>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.orderQuantity = value;
      }

      state.items = items;
    },
    setItemIsApproximate(
      state,
      { payload }: PayloadAction<SetItemArgs<boolean>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.isApproximate = value;
      }

      state.items = items;
    },
    setItemVendor(
      state,
      { payload }: PayloadAction<SetItemArgs<spire.Vendor | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.vendorId = value?.id || null;
        item.vendorName = value?.name || null;
        if (item.comments === prebooks.overAndAboveBlanketComment) {
          item.comments = null;
        }

        const productDefault = state.productDefaults.find(
            (d) => d.spireInventoryId === item.spireInventoryId
          ),
          season =
            state.seasons.find((s) => s.name === state.seasonName) || null,
          boekestynProducts = getBoekestynProducts(
            payload.value?.id,
            state.requiredDate,
            season,
            productDefault
          );

        item.boekestynProducts = boekestynProducts;
      }

      state.items = items;
    },
    setItemCreatePrebook(
      state,
      { payload }: PayloadAction<SetItemArgs<boolean>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.createPrebook = value;
      }

      state.items = items;
    },
    setItemBlanketItemId(
      state,
      { payload }: PayloadAction<SetItemArgs<number | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.blanketItemId = value;
        if (value && !item.vendorId) {
          const blanketItem = state.blanketItems.find(
            (i) => i.id === item.blanketItemId
          );
          if (blanketItem) {
            item.vendorId = blanketItem.vendorId;
            item.vendorName = blanketItem.vendorName;
          }
        }
      }

      state.items = items;
    },
    setItemIsBlanket(state, { payload }: PayloadAction<SetItemArgs<boolean>>) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.isBlanket = value;
      }

      state.items = items;
    },
    setItemUnitPrice(
      state,
      { payload }: PayloadAction<SetItemArgs<number | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.unitPrice = value;
      }

      state.items = items;
    },
    setItemSpecialPrice(
      state,
      { payload }: PayloadAction<SetItemArgs<number | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.specialPrice = value;
      }

      state.items = items;
    },
    setItemGrowerItemNotes(
      state,
      { payload }: PayloadAction<SetItemArgs<string | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.growerItemNotes = value;
      }

      state.items = items;
    },
    setItemUseAvailabilityPricing(
      state,
      { payload }: PayloadAction<SetItemArgs<boolean>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.useAvailabilityPricing = value;
        if (value) {
          item.unitPrice = null;
        }
      }

      state.items = items;
    },
    setItemCustomerItemCode(
      state,
      { payload }: PayloadAction<SetItemArgs<string | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.customerItemCode = value;
      }

      state.items = items;
    },
    setItemUpgradeSheet(
      state,
      { payload }: PayloadAction<SetItemArgs<boolean>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.upgradeSheet = value;
      }

      state.items = items;
    },
    setItemPhytoRequired(
      state,
      { payload }: PayloadAction<SetItemArgs<boolean>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.phytoRequired = value;
      }

      state.items = items;
    },
    setItemBoekestynProductionOrder(
      state,
      {
        payload,
      }: PayloadAction<
        SetItemArgs<{
          boekestynPlantId: string | null;
          boekestynCustomerAbbreviation: string | null;
        }>
      >
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.boekestynPlantId = value.boekestynPlantId;
        item.boekestynCustomerAbbreviation =
          value.boekestynCustomerAbbreviation;
      }

      state.items = items;
    },
    setItemUpgradeLabourHours(
      state,
      { payload }: PayloadAction<SetItemArgs<number>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.upgradeLabourHours = value;
      }

      state.items = items;
    },
    setItemBoekestynProducts(
      state,
      {
        payload,
      }: PayloadAction<SetItemArgs<models.FutureOrderBoekestynProduct[]>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.boekestynProducts = value;
      }

      state.items = items;
    },
    setItemQuantityPerFinishedItem(
      state,
      { payload }: PayloadAction<SetItemArgs<number | null>>
    ) {
      const items = state.items.map((i) => ({ ...i })),
        { itemId, value } = payload,
        item = items.find((i) => i.id === itemId);

      if (item) {
        item.quantityPerFinishedItem = value;
      }

      state.items = items;
    },
    setItemAvailabilityPrice(
      state,
      { payload }: PayloadAction<SetItemArgs<number | null | undefined>>
    ) {
      const availabilityUnitPrices = { ...state.availabilityUnitPrices };

      if (payload.value) {
        availabilityUnitPrices[payload.itemId] = payload.value;
      } else {
        delete availabilityUnitPrices[payload.itemId];
      }

      state.availabilityUnitPrices = availabilityUnitPrices;
    },
    setAllWeightsAndMeasures(state, { payload }: PayloadAction<boolean>) {
      const items = state.items.map((i) => ({
        ...i,
        weightsAndMeasures: payload,
      }));
      state.items = items;
    },
    setAllUseAvailabilityPricing(state, { payload }: PayloadAction<boolean>) {
      const items = state.items.map((i) => ({
        ...i,
        useAvailabilityPricing: payload,
        unitPrice: payload ? null : i.unitPrice,
      }));

      state.items = items;
    },
    setAllDateCodes(state, { payload }: PayloadAction<string | null>) {
      const items = state.items.map((i) => ({
        ...i,
        dateCode: payload,
      }));
      state.items = items;
    },
    setAllHasPotCovers(state, { payload }: PayloadAction<boolean>) {
      const items = state.items.map((i) => ({ ...i }));

      items
        .filter((i) => !noPotCover(i.spirePartNumber, i.description))
        .forEach((i) => {
          i.hasPotCover = payload;
          i.potCover = payload ? i.potCover : null;
        });

      state.items = items;
    },
    setAllPotCovers(state, { payload }: PayloadAction<string | null>) {
      const items = state.items.map((i) => ({ ...i }));

      items
        .filter((i) => !noPotCover(i.spirePartNumber, i.description))
        .forEach((i) => (i.potCover = payload));

      state.items = items;
    },
    setAllRetails(state, { payload }: PayloadAction<string | null>) {
      const items = state.items.map((i) => ({ ...i, retail: payload }));
      state.items = items;
    },
    setAllUpcs(state, { payload }: PayloadAction<string | null>) {
      const items = state.items.map((i) => ({ ...i, upc: payload }));
      state.items = items;
    },
    setAllUnitPrices(state, { payload }: PayloadAction<number | null>) {
      const items = state.items.map((i) => ({ ...i, unitPrice: payload }));
      state.items = items;
    },
    moveItem(state, { payload }: PayloadAction<MoveItemArgs>) {
      const { movingItem, existingItem } = payload,
        copy = state.items
          .filter((i) => i.id !== movingItem.id)
          .map((i) => ({ ...i })),
        index = copy.findIndex((i) => i.id === existingItem.id);

      if (movingItem) {
        copy.splice(index, 0, { ...movingItem });

        copy.forEach((i, index) => (i.sortOrder = index + 1));

        state.items = copy;
      }
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(getCustomerDetailFulfilled, (state, { payload }) => {
        if (payload) {
          const {
              customer: customerDetail,
              customerItemCodes,
              potCovers,
            } = payload,
            customer = {
              id: customerDetail.id,
              customerNo: customerDetail.customerNo,
              name: customerDetail.name,
              defaultShipTo: customerDetail.defaultShipTo,
            },
            shipTo =
              customerDetail.shippingAddresses.find(
                (a) => a.shipId === customerDetail.defaultShipTo
              ) || null;

          state.customerDetail = customerDetail;
          state.customer = customer;
          state.shipTo = shipTo;
          state.customerItemCodeDefaults = customerItemCodes;
          state.salesperson =
            state.salespeople.find(
              (s) => s.code === shipTo?.salesperson?.code
            ) || null;
          state.boxCode = shipTo?.boxCode || null;
          state.customerPotCovers = potCovers;
          state.requiresLabels = equals(models.SpecialLabels, shipTo?.labels);
        } else {
          state.customerDetail = null;
          state.customer = null;
          state.shipTo = null;
          state.customerItemCodeDefaults = [];
          state.salesperson = null;
          state.boxCode = null;
          state.customerPotCovers = [];
        }
      })
      .addCase(getCustomerDetailRejected, (state, { payload }) => {
        state.error = payload;
      })
      .addCase(createFutureOrderRejected, (state, { payload }) => {
        state.error = payload;
      })
      .addCase(copyFutureOrderPending, (state) => {
        state.isLoading = true;
      })
      .addCase(copyFutureOrderFulfilled, (state, { payload }) => {
        state.isLoading = false;

        const {
          seasonName,
          salespersonId,
          salespersonName,
          shipToId,
          shipToName,
          customerId,
          customerName,
          shipViaId,
          shipViaName,
          boxCode,
          requiresLabels,
          spireNotes,
          comments,
          items,
        } = payload.futureOrder;

        let id = 0;

        state.seasonName = seasonName;
        if (salespersonId && salespersonName) {
          state.salesperson = {
            id: salespersonId,
            name: salespersonName,
            code: '',
          };
        }
        if (shipToId && shipToName) {
          state.shipTo = {
            id: shipToId,
            name: shipToName,
            boxCode: null,
            labels: null,
            salesperson: null,
            shipId: shipToName,
            customerInfo: null,
          };
        }
        if (customerId && customerName) {
          state.customer = {
            id: customerId,
            name: customerName,
            defaultShipTo: null,
          };
        }
        if (shipViaId && shipViaName) {
          state.shipVia = { id: shipViaId, description: shipViaName, code: '' };
        }
        state.boxCode = boxCode;
        state.requiresLabels = requiresLabels;
        state.spireNotes = spireNotes;
        state.comments = comments
          .filter((c) => c.isStandardComment)
          .map((c, i) => ({ ...c, id: -i }));
        state.internalComments =
          comments.find((c) => !c.isStandardComment)?.comments || null;
        state.items = items
          .map((i) => {
            const blanketItem = state.blanketItems.filter(
                (bi) =>
                  (!i.vendorId || bi.vendorId === i.vendorId) &&
                  (!bi.blanketStartDate ||
                    !state.requiredDate ||
                    bi.blanketStartDate <= state.requiredDate) &&
                  (!state.requiredDate ||
                    !bi.requiredDate ||
                    bi.requiredDate >= state.requiredDate) &&
                  bi.spireInventoryId === i.spireInventoryId &&
                  (!customerId ||
                    !bi.customerId ||
                    customerId === bi.customerId) &&
                  (!shipToId || !bi.shipToId || shipToId === bi.shipToId)
              )[0],
              productDefault = state.productDefaults.find(
                (pd) => pd.spireInventoryId === i.spireInventoryId
              ),
              upgradeLabourHours = productDefault?.upgradeLabourHours || 0,
              quantityPerFinishedItem =
                productDefault?.quantityPerFinishedItem || null,
              season =
                state.seasons.find((s) => s.name === state.seasonName) || null,
              boekestynProducts = getBoekestynProducts(
                i.vendorId,
                state.requiredDate,
                season,
                productDefault
              );

            state.comments = payload.futureOrder.comments.map((c, idx) => ({
              ...c,
              id: -idx,
            }));

            return {
              id: id--,
              sortOrder: i.sortOrder,
              vendorId: i.vendorId,
              vendorName: i.vendorName,
              spireInventoryId: i.spireInventoryId,
              spirePartNumber: i.spirePartNumber,
              description: i.description,
              orderQuantity: 0,
              isApproximate: false,
              hasPotCover: i.hasPotCover,
              potCover: i.potCover,
              dateCode: null,
              upc: i.upc,
              weightsAndMeasures: i.weightsAndMeasures,
              retail: i.retail,
              comments: i.comments,
              createPrebook: true,
              isBlanket: false,
              blanketItemId: blanketItem?.id || null,
              unitPrice: i.unitPrice,
              useAvailabilityPricing: i.useAvailabilityPricing,
              customerItemCode: i.customerItemCode,
              upgradeSheet: i.upgradeSheet,
              phytoRequired: i.phytoRequired,
              boekestynPlantId: i.boekestynPlantId,
              boekestynCustomerAbbreviation: i.boekestynCustomerAbbreviation,
              upgradeLabourHours,
              quantityPerFinishedItem,
              specialPrice: i.specialPrice,
              growerItemNotes: i.growerItemNotes,
              boekestynProducts,
            };
          })
          .sort(sortBySortOrder);
      })
      .addCase(copyFutureOrderRejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      .addMatcher(
        prebookListApi.endpoints.openBlanketItems.matchFulfilled,
        (state, { payload }) => {
          state.blanketItems = payload.blanketItems;
        }
      )
      .addMatcher(
        spireApi.endpoints.inventoryItems.matchFulfilled,
        (state, { payload }) => {
          state.productDefaults = payload.productDefaults;
        }
      )
      .addMatcher(
        spireApi.endpoints.salespeople.matchFulfilled,
        (state, { payload }) => {
          state.salespeople = payload;
        }
      )
      .addMatcher(
        spireApi.endpoints.vendors.matchFulfilled,
        (state, { payload }) => {
          state.vendors = payload;
        }
      )
      .addMatcher(
        seasonsApi.endpoints.seasons.matchFulfilled,
        (state, { payload }) => {
          state.seasons = payload;
        }
      )
      .addMatcher(
        settingsApi.endpoints.defaultVendorOverrides.matchFulfilled,
        (state, { payload }) => {
          state.defaultVendorOverrides = payload;
        }
      )
      .addMatcher(
        futureOrdersListApi.endpoints.holidays.matchFulfilled,
        (state, { payload }) => {
          state.holidays = payload;
        }
      ),
});

export const {
  clearState,
  clearError,
  setError,
  setShipTo,
  setCustomerInfo,
  setCustomerDetail,
  setSalesperson,
  setRequiredDate,
  setArrivalDate,
  setSeasonName,
  setShipVia,
  setBoxCode,
  setRequiresLabels,
  setCustomerPurchaseOrderNumber,
  setFreightPerCase,
  setFreightPerLoad,
  setFreightIsActual,
  setComments,
  setInternalComments,
  setGrowerItemNotes,
  setSpireNotes,
  addItem,
  duplicateItem,
  removeItem,
  setItemExpanded,
  setAllExpanded,
  setItems,
  setShowInventoryDialog,
  setItemInventoryItem,
  setItemHasPotCover,
  setItemPotCover,
  setItemDateCode,
  setItemUPC,
  setItemWeightsAndMeasures,
  setItemRetail,
  setItemComments,
  setItemOrderQuantity,
  setItemIsApproximate,
  setItemVendor,
  setItemCreatePrebook,
  setItemBlanketItemId,
  setItemIsBlanket,
  setItemUnitPrice,
  setItemSpecialPrice,
  setItemGrowerItemNotes,
  setItemUseAvailabilityPricing,
  setItemCustomerItemCode,
  setItemUpgradeSheet,
  setItemPhytoRequired,
  setItemBoekestynProductionOrder,
  setItemUpgradeLabourHours,
  setItemBoekestynProducts,
  setItemQuantityPerFinishedItem,
  setItemAvailabilityPrice,
  setAllWeightsAndMeasures,
  setAllUseAvailabilityPricing,
  setAllHasPotCovers,
  setAllPotCovers,
  setAllDateCodes,
  setAllRetails,
  setAllUpcs,
  setAllUnitPrices,
  moveItem,
} = futureOrderCreateSlice.actions;

export const selectError = (state: RootState) => state.futureOrderCreate.error;
export const selectCustomer = (state: RootState) =>
  state.futureOrderCreate.customer;
export const selectSalesperson = (state: RootState) =>
  state.futureOrderCreate.salesperson;
export const selectShipTo = (state: RootState) =>
  state.futureOrderCreate.shipTo;
export const selectShipVia = (state: RootState) =>
  state.futureOrderCreate.shipVia;
export const selectRequiredDate = (state: RootState) =>
  state.futureOrderCreate.requiredDate;
export const selectArrivalDate = (state: RootState) =>
  state.futureOrderCreate.arrivalDate;
export const selectSeasonName = (state: RootState) =>
  state.futureOrderCreate.seasonName;
export const selectBoxCode = (state: RootState) =>
  state.futureOrderCreate.boxCode;
export const selectRequiresLabels = (state: RootState) =>
  state.futureOrderCreate.requiresLabels;
export const selectCustomerPurchaseOrderNumber = (state: RootState) =>
  state.futureOrderCreate.customerPurchaseOrderNumber;
export const selectFreightPerCase = (state: RootState) =>
  state.futureOrderCreate.freightPerCase;
export const selectFreightPerLoad = (state: RootState) =>
  state.futureOrderCreate.freightPerLoad;
export const selectFreightIsActual = (state: RootState) =>
  state.futureOrderCreate.freightIsActual;
export const selectGrowerItemNotes = (state: RootState) =>
  state.futureOrderCreate.growerItemNotes;
export const selectSpireNotes = (state: RootState) =>
  state.futureOrderCreate.spireNotes;
export const selectComments = (state: RootState) =>
  state.futureOrderCreate.comments;
export const selectInternalComments = (state: RootState) =>
  state.futureOrderCreate.internalComments;
export const selectCustomerInfo = (state: RootState) =>
  state.futureOrderCreate.customerInfo;
export const selectItems = (state: RootState) => state.futureOrderCreate.items;
export const selectShowInventoryDialog = (state: RootState) =>
  state.futureOrderCreate.showInventoryDialog;
export const selectIsLoading = (state: RootState) =>
  state.futureOrderCreate.isLoading;
export const selectCustomerDetail = (state: RootState) =>
  state.futureOrderCreate.customerDetail;
export const selectCustomerItemCodeDefaults = (state: RootState) =>
  state.futureOrderCreate.customerItemCodeDefaults;
export const selectPotCovers = (state: RootState) => {
  const customerPotCovers = state.futureOrderCreate.customerPotCovers,
    potCovers = defaultPotCovers.concat(customerPotCovers);
  return potCovers;
};
export const selectProductDefaults = (state: RootState) =>
  state.futureOrderCreate.productDefaults;
export const selectSeasons = (state: RootState) =>
  state.futureOrderCreate.seasons;
export const selectAvailabilityUnitPrices = (state: RootState) =>
  state.futureOrderCreate.availabilityUnitPrices;
export const selectDefaultVendorOverrides = (state: RootState) =>
  state.futureOrderCreate.defaultVendorOverrides;
export const selectShipTos = createSelector(
  selectCustomerDetail,
  (customerDetail) => customerDetail?.shippingAddresses || []
);

export const selectHoliday = createSelector(
  (state: RootState) => state.futureOrderDetail.holidays,
  selectRequiredDate,
  (holidays, requiredDate) =>
    (!!requiredDate &&
      holidays.find(
        (h) => h.date === requiredDate || h.observedDate === requiredDate
      )) ??
    null
);

export default futureOrderCreateSlice.reducer;
