import React, { useEffect, useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import * as HeadlessUI from '@headlessui/react';
import { useItemListQuery } from 'api/boekestyn-service';
import * as models from 'api/models/boekestyns';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { routes } from '@/services/routes';
import {
  clearError,
  clearSearchAndFilters,
  setStartDate,
  setEndDate,
  setSearch,
  setCustomerFilter,
  setShipToFilter,
  selectStartDate,
  selectEndDate,
  selectSearch,
  selectSort,
  selectSortDescending,
  selectIsLoading,
  selectError,
  selectItems,
  selectItemDates,
  selectItemCustomers,
  selectItemShipTos,
  selectFilter,
  setSort,
  downloadItemList,
  setHighlightPrintedUPCs,
  selectHighlightPrintedUPCs,
} from '@/components/boekestyns/boekestyn-list-slice';
import { DateItem } from '@/components/boekestyns/item-date-item';
import { Error } from '@/components/error';
import { Icon } from '@/components/icon';
import { Loading } from '@/components/loading';
import { classNames } from '@/utils/class-names';

// 10 minutes
const RefreshInterval = 10 * 60 * 1000;

export default function FutureOrders() {
  const dispatch = useAppDispatch(),
    itemDates = useAppSelector(selectItemDates),
    items = useAppSelector(selectItems),
    customers = useAppSelector(selectItemCustomers),
    shipTos = useAppSelector(selectItemShipTos),
    startDate = useAppSelector(selectStartDate),
    endDate = useAppSelector(selectEndDate),
    search = useAppSelector(selectSearch),
    sort = useAppSelector(selectSort),
    sortDescending = useAppSelector(selectSortDescending),
    isLoading = useAppSelector(selectIsLoading),
    error = useAppSelector(selectError),
    filter = useAppSelector(selectFilter),
    highlightPrintedUPCs = useAppSelector(selectHighlightPrintedUPCs),
    { refetch } = useItemListQuery({ startDate, endDate }),
    [showAdvanced, setShowAdvanced] = useState(
      !!filter.customer || !!filter.shipTo
    ),
    [timer, setTimer] = useState<number | null>(null);

  useEffect(() => {
    if (!timer) {
      const interval = window.setInterval(refetch, RefreshInterval);
      setTimer(interval);
    }

    return function cleanup() {
      if (timer) {
        window.clearInterval(timer);
      }
    };
  }, [refetch, timer]);

  useEffect(() => {
    const highlightPrintedUPCs = !!window.localStorage.getItem(
      'highlightPrintedUPCs'
    );
    dispatch(setHighlightPrintedUPCs(highlightPrintedUPCs));
  }, [dispatch]);

  const handleDownloadClick = () => {
    dispatch(downloadItemList(items));
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSearch(e.target.value));
  };

  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setStartDate(e.target.value || ''));
  };

  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setEndDate(e.target.value || ''));
  };

  const handleResetSearchClick = async () => {
    await dispatch(clearSearchAndFilters());
    window.setTimeout(refetch);
  };

  const handleRefreshClick = () => {
    refetch();
  };

  const handleClearError = () => {
    dispatch(clearError());
  };

  const handleColumnSort = (sortProp: keyof models.ItemListItem) => {
    const descending = sortProp === sort ? !sortDescending : false;
    dispatch(setSort({ sort: sortProp, sortDescending: descending }));
  };

  const handleHighlightPrintedUPCsChange = (highlight: boolean) => {
    dispatch(setHighlightPrintedUPCs(highlight));
  };

  const handleToggleAdvanced = () => {
    setShowAdvanced(!showAdvanced);
    if (showAdvanced) {
      dispatch(setCustomerFilter(null));
      dispatch(setShipToFilter(null));
    }
  };

  const handleCustomerFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setCustomerFilter(e.target.value || null));
  };

  const handleShipToFilterChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    dispatch(setShipToFilter(e.target.value || null));
  };

  interface HeaderButtonProps {
    text: string;
    propName: keyof models.ItemListItem;
  }
  const HeaderButton = ({ text, propName }: HeaderButtonProps) => (
    <button
      type="button"
      className="group inline-flex"
      onClick={() => handleColumnSort(propName)}
    >
      {text}
      <span
        className={classNames(
          'ml-2 flex-none rounded text-gray-400 group-hover:visible group-focus:visible',
          sort !== propName && 'invisible'
        )}
      >
        <Icon
          icon={sortDescending ? 'chevron-down' : 'chevron-up'}
          className="h-5 w-5"
          aria-hidden="true"
        />
      </span>
    </button>
  );

  return (
    <>
      <Head>
        <title>Boekestyn Item List</title>
      </Head>
      <header className="bg-white shadow">
        <div className="grid grid-cols-1">
          <div className="mb-4 hidden border-b shadow md:block">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-2 md:px-8">
              <div className="flex">
                <nav className="ml-24 flex flex-row">
                  <div className="group relative my-2 text-nowrap rounded-lg bg-blue-500 p-2 text-center text-sm font-medium text-white">
                    Boekestyn Item List
                  </div>
                  <Link
                    href={routes.boekestyns.sales.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Boekestyn Sales
                  </Link>
                  <Link
                    href={routes.boekestyns.sticking.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Sticking
                  </Link>
                  <Link
                    href={routes.boekestyns.spacing.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Spacing
                  </Link>
                  <Link
                    href={routes.boekestyns.spacing.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Harvesting
                  </Link>
                  <Link
                    href={routes.boekestyns.upcs.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    UPCs
                  </Link>
                  <Link
                    href={routes.boekestyns.prep.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Order Prep
                  </Link>
                  <Link
                    href={routes.boekestyns.packing.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Order Packing
                  </Link>
                  <Link
                    href={routes.boekestyns.admin.to()}
                    className="group relative m-2 rounded-lg bg-white p-2 text-center text-sm font-medium text-gray-500 hover:text-blue-500 focus:z-10"
                  >
                    Admin
                  </Link>
                </nav>
              </div>
            </div>
          </div>
          <div className="bg-gray-100 py-2">
            <div className="mx-auto flex max-w-7xl items-center justify-between px-2 md:px-8">
              <div className="flex flex-grow items-center align-top">
                <div className="flex w-full flex-col rounded p-2">
                  <div className="grid w-full grid-cols-4 gap-2 rounded-sm text-xs md:grid-cols-8">
                    <div className="col-span-2 md:col-span-1">
                      <label htmlFor="start-date">From Date</label>
                      <input
                        type="date"
                        max="2050-01-01"
                        id="start-date"
                        value={startDate}
                        onChange={handleStartDateChange}
                        className="w-full !max-w-none text-xs"
                      />
                    </div>
                    <div className="col-span-2 md:col-span-1">
                      <label htmlFor="end-date">To Date</label>
                      <input
                        type="date"
                        max="2050-01-01"
                        id="end-date"
                        value={endDate}
                        onChange={handleEndDateChange}
                        className="w-full !max-w-none text-xs"
                      />
                    </div>
                    <div className="col-span-4">
                      <label htmlFor="end-date">Search</label>
                      <div className="flex">
                        <input
                          type="search"
                          id="search"
                          name="search"
                          value={search}
                          onChange={handleSearchChange}
                          className="flex-grow text-xs"
                          placeholder="Search"
                          autoComplete="off"
                        />
                        <button
                          type="button"
                          className="btn-secondary flex px-1 focus:ring-0"
                          onClick={handleToggleAdvanced}
                        >
                          <Icon
                            icon={showAdvanced ? 'chevron-up' : 'chevron-down'}
                            className="h-5 w-5"
                          />
                        </button>
                        <button
                          type="button"
                          className="btn-secondary flex px-1 focus:ring-0"
                          title="Reset Search Filters"
                          onClick={handleResetSearchClick}
                        >
                          <Icon
                            icon="magnifying-glass-arrows-rotate"
                            className="h-5 w-5"
                          />
                        </button>
                      </div>
                    </div>
                    <div className="col-span-4 grid grid-cols-2 gap-x-4 pt-3 md:col-span-1 md:flex md:items-start md:justify-center">
                      <button
                        type="button"
                        onClick={handleRefreshClick}
                        className="btn-secondary flex justify-center text-center text-blue-700 md:p-3"
                      >
                        <Icon icon="refresh" spin={isLoading} />
                        <span className="md:hidden">&nbsp;Refresh</span>
                      </button>
                      <button
                        type="button"
                        onClick={handleDownloadClick}
                        className="btn-secondary flex justify-center text-center text-green-700 md:p-3"
                      >
                        <Icon icon="file-excel" />
                        <span className="md:hidden">&nbsp;Download</span>
                      </button>
                    </div>
                    <div className="hidden grid-cols-1 md:flex">
                      <div>
                        <label
                          htmlFor="highlight-printed-upcs"
                          className="block w-full text-center"
                        >
                          Highlight Printed UPCs
                        </label>
                        <div className="mt-3 text-center align-middle">
                          <HeadlessUI.Switch
                            id="highlight-printed-upcs"
                            checked={highlightPrintedUPCs}
                            onChange={handleHighlightPrintedUPCsChange}
                            className={`${
                              highlightPrintedUPCs
                                ? 'bg-blue-600'
                                : 'bg-gray-200'
                            } relative inline-flex h-6 w-11 items-center rounded-full`}
                          >
                            <span className="sr-only">
                              Highlight Printed UPCs
                            </span>
                            <span
                              className={`${
                                highlightPrintedUPCs
                                  ? 'translate-x-6'
                                  : 'translate-x-1'
                              } inline-block h-4 w-4 transform rounded-full bg-white transition`}
                            />
                          </HeadlessUI.Switch>
                        </div>
                      </div>
                    </div>
                    {showAdvanced && (
                      <>
                        <div className="col-start-1">
                          <label htmlFor="customer-filter">Customer</label>
                          <select
                            id="customer-filter"
                            value={filter.customer || ''}
                            onChange={handleCustomerFilterChange}
                            className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                          >
                            <option value="">All Customers</option>
                            {customers.map((customer) => (
                              <option key={customer}>{customer}</option>
                            ))}
                          </select>
                        </div>
                        <div className="">
                          <label htmlFor="ship-to-filter">Ship To</label>
                          <select
                            id="ship-to-filter"
                            value={filter.shipTo || ''}
                            onChange={handleShipToFilterChange}
                            className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-xs focus:border-blue-500 focus:outline-none focus:ring-blue-500"
                          >
                            <option value="">All Ship Tos</option>
                            {shipTos.map((shipTo) => (
                              <option key={shipTo}>{shipTo}</option>
                            ))}
                          </select>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
      <main className="flex-grow overflow-auto">
        <div className="mx-auto h-full md:px-8">
          <Error error={error} clear={handleClearError} />
          {isLoading && <Loading />}
          <div className="flex h-full flex-col md:mt-8">
            <div className="h-full md:-mx-8 md:-my-2">
              <div className="inline-block min-w-full py-2 align-middle md:px-8">
                <div className="rounded-lg shadow ring-1 ring-black ring-opacity-5">
                  <table className="min-w-full divide-y divide-gray-300">
                    <thead>
                      <tr className="sticky top-0 z-10">
                        <th
                          scope="col"
                          className="bg-gray-100 px-1 py-2 text-center text-xs font-semibold text-gray-900"
                        >
                          <HeaderButton text="PB" propName="prebookId" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-1 py-2 text-left text-xs font-semibold text-gray-900"
                        >
                          <HeaderButton text="Date" propName="requiredDate" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-1 py-2 text-left text-xs font-semibold text-gray-900"
                        >
                          <HeaderButton text="Customer" propName="customer" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-1 py-2 text-center text-xs font-semibold text-gray-900"
                        >
                          <HeaderButton text="Cases" propName="caseCount" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-1 py-2 text-left text-xs font-semibold text-gray-900"
                        >
                          <HeaderButton text="Pack" propName="packQuantity" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-1 py-2 text-left text-xs font-semibold text-gray-900"
                        >
                          <HeaderButton
                            text="Product"
                            propName="spirePartNumber"
                          />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-1 py-2 text-center text-xs font-semibold text-gray-900"
                        >
                          <HeaderButton text="Potcover" propName="potCover" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-1 py-2 text-center text-xs font-semibold text-gray-900"
                        >
                          <HeaderButton text="Code" propName="boxCode" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-1 py-2 text-center text-xs font-semibold text-gray-900"
                        >
                          <HeaderButton text="UPC" propName="upc" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-1 py-2 text-center text-xs font-semibold text-gray-900"
                        >
                          <HeaderButton text="Date Code" propName="dateCode" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-1 py-2 text-center text-xs font-semibold text-gray-900"
                        >
                          <HeaderButton text="Retail" propName="retail" />
                        </th>
                        <th
                          scope="col"
                          className="bg-gray-100 px-1 py-2 text-center text-xs font-semibold text-gray-900"
                        >
                          <HeaderButton
                            text="W&M"
                            propName="weightsAndMeasures"
                          />
                        </th>
                      </tr>
                    </thead>
                    {itemDates.map(({ date, season }) => (
                      <DateItem
                        key={`${date}-${season}`}
                        date={date}
                        season={season}
                      />
                    ))}
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  );
}
