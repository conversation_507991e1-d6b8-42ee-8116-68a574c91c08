import { AxiosError, AxiosResponseHeaders, ResponseType } from 'axios';
import { serializeError } from 'serialize-error';
import { createApi } from '@reduxjs/toolkit/query/react';
import axios, {
  ApiBase,
  axiosBaseQuery,
  downloadFile,
  getFilename,
} from './api-base';
import * as models from './models/future-orders';
import * as prebooks from './models/prebooks';
import * as settings from './models/settings';
import * as holidays from './models/holidays';
import {
  createProblemDetails,
  isProblemDetails,
} from '@/utils/problem-details';

const type =
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  responseType: ResponseType = 'blob';

class FutureOrdersService extends ApiBase {
  detail(id: number): Promise<FutureOrderDetailResponse> {
    return this.get(`future-orders/${id}`);
  }

  create(data: models.FutureOrderCreate): Promise<FutureOrderCreateResponse> {
    return this.post('future-orders', data);
  }

  update(
    futureOrder: models.FutureOrderUpdate,
    prebooks: prebooks.PrebookDetail[]
  ): Promise<FutureOrderDetailResponse> {
    const data = { futureOrder, prebooks };
    return this.put(`future-orders/${futureOrder.id}`, data);
  }

  split(
    id: number,
    data: models.FutureOrderSplit
  ): Promise<FutureOrderCreateResponse> {
    return this.post(`future-orders/${id}/split`, data);
  }

  sendToSpire(id: number): Promise<void> {
    return this.post(`future-orders/${id}/spire`);
  }

  deleted(): Promise<DeletedResponse> {
    return this.get('future-orders/deleted');
  }

  deleteOrder(id: number): Promise<void> {
    return this.put(`future-orders/${id}/delete`);
  }

  undeleteOrder(id: number): Promise<void> {
    return this.put(`future-orders/${id}/undelete`);
  }

  updateUpgradeItemProperty({
    id,
    value: stringValue,
    fieldName,
  }: UpdateUpgradeItemPropertyArgs) {
    const isNumeric = typeof stringValue === 'number',
      isBoolean = typeof stringValue === 'boolean',
      value = isNumeric || isBoolean ? null : stringValue,
      valueAsNumber = isNumeric ? stringValue : 0,
      valueAsBoolean = isBoolean ? stringValue : false,
      data = {
        id,
        fieldName,
        value,
        valueAsNumber,
        valueAsBoolean,
        isNumeric,
        isBoolean,
      };

    return this.patch(`future-orders/upgrade-items/${id}`, data);
  }

  async futureOrderListDownload(data: FutureOrderListDownloadArgs) {
    try {
      const { headers, data: result } = await axios.post(
          '/future-orders/download',
          data,
          {
            responseType,
          }
        ),
        blob = new Blob([result], { type }),
        filename =
          getFilename(headers as AxiosResponseHeaders) || 'FutureOrders.xlsx';

      downloadFile(blob, filename);
    } catch (ex) {
      if (ex instanceof Error) {
        const error = serializeError(ex);
        throw createProblemDetails(error.message || 'Error');
      }
      const e = ex as AxiosError;
      if (e.response) {
        if (isProblemDetails(e.response.data)) {
          throw e.response.data;
        } else if (typeof e.response.data === 'string') {
          throw createProblemDetails(e.response.data);
        }
      }

      if (e.response && e.response.data) {
        throw e.response.data;
      }

      throw e.message;
    }
  }

  async futureOrderItemsListDownload(data: FutureOrderItemsListDownloadArgs) {
    try {
      const { headers, data: result } = await axios.post(
          '/future-orders/items/download',
          data,
          {
            responseType,
          }
        ),
        blob = new Blob([result], { type }),
        filename =
          getFilename(headers as AxiosResponseHeaders) ||
          'FutureOrderItems.xlsx';

      downloadFile(blob, filename);
    } catch (ex) {
      if (ex instanceof Error) {
        const error = serializeError(ex);
        throw createProblemDetails(error.message || 'Error');
      }
      const e = ex as AxiosError;
      if (e.response) {
        if (isProblemDetails(e.response.data)) {
          throw e.response.data;
        } else if (typeof e.response.data === 'string') {
          throw createProblemDetails(e.response.data);
        }
      }

      if (e.response && e.response.data) {
        throw e.response.data;
      }

      throw e.message;
    }
  }

  async futureOrderUpgradeItemsListDownload({
    filename,
    items,
  }: FutureOrderUpgradeItemsListDownloadArgs) {
    try {
      const { headers, data: result } = await axios.post(
          '/future-orders/upgrade-items/download',
          { items },
          {
            responseType,
          }
        ),
        blob = new Blob([result], { type });

      downloadFile(blob, filename);
    } catch (ex) {
      if (ex instanceof Error) {
        const error = serializeError(ex);
        throw createProblemDetails(error.message || 'Error');
      }
      const e = ex as AxiosError;
      if (e.response) {
        if (isProblemDetails(e.response.data)) {
          throw e.response.data;
        } else if (typeof e.response.data === 'string') {
          throw createProblemDetails(e.response.data);
        }
      }

      if (e.response && e.response.data) {
        throw e.response.data;
      }

      throw e.message;
    }
  }

  async futureOrderUpgradeItemsListReport({
    filename,
    items,
  }: FutureOrderUpgradeItemsListDownloadArgs) {
    try {
      const { headers, data: result } = await axios.post(
          '/future-orders/upgrade-items/report',
          { items },
          {
            responseType,
          }
        ),
        blob = new Blob([result], { type });

      downloadFile(blob, filename);
    } catch (ex) {
      if (ex instanceof Error) {
        const error = serializeError(ex);
        throw createProblemDetails(error.message || 'Error');
      }
      const e = ex as AxiosError;
      if (e.response) {
        if (isProblemDetails(e.response.data)) {
          throw e.response.data;
        } else if (typeof e.response.data === 'string') {
          throw createProblemDetails(e.response.data);
        }
      }

      if (e.response && e.response.data) {
        throw e.response.data;
      }

      throw e.message;
    }
  }

  createEmail(
    id: number,
    to: string,
    cc: string | null,
    bcc: string | null,
    subject: string,
    body: string
  ): Promise<void> {
    const data = { to, cc, bcc, subject, body };
    return this.post(`future-orders/${id}/customer-confirmation/email`, data);
  }
}

export const futureOrdersApi = new FutureOrdersService();

export const futureOrdersListApi = createApi({
  reducerPath: 'future-order-list-api',
  baseQuery: axiosBaseQuery('future-orders/'),
  refetchOnMountOrArgChange: true,
  endpoints: (builder) => ({
    list: builder.query<FutureOrderListResponse, FutureOrderListArgs>({
      query: ({ startDate, endDate }) => ({
        url: `?startDate=${startDate}&endDate=${endDate}`,
      }),
    }),
    itemSummary: builder.query<ItemSummaryResponse, ItemSummaryArgs>({
      query: ({ startDate, endDate }) => ({
        url: `items?startDate=${startDate}&endDate=${endDate}`,
      }),
    }),
    upgradeItems: builder.query<UpgradeItemsResponse, UpgradeItemsArgs>({
      query: ({ startDate, endDate }) => ({
        url: `upgrade-items?startDate=${startDate}&endDate=${endDate}`,
      }),
    }),
    itemPrice: builder.query<number | null, ItemPriceArgs>({
      query: ({ spireInventoryId, customerId, shipToId, requiredDate }) => ({
        url:
          `items/${spireInventoryId}/price` +
          (customerId ? `?customerId=${customerId}` : '') +
          (shipToId ? `&shipToId=${shipToId}` : '') +
          (requiredDate ? `&requiredDate=${requiredDate}` : ''),
      }),
      transformResponse: ({ price }: ItemPriceResponse) => price,
    }),
    itemPrices: builder.query<SpireItemPrice[], SpireItemPricesArgs>({
      query: ({
        customerId,
        shipToId,
        items,
        requiredDate,
      }: SpireItemPricesArgs) => ({
        url:
          'items/prices?' +
          items.map((i) => `${i}`).join('&') +
          (customerId ? `&customerId=${customerId}` : '') +
          (shipToId ? `&shipToId=${shipToId}` : '') +
          (requiredDate ? `&requiredDate=${requiredDate}` : ''),
      }),
      transformResponse: ({ prices }: SpireItemPricesResponse) => prices,
    }),
    children: builder.query<models.FutureOrderChild[], number>({
      query: (id) => ({ url: `${id}/children` }),
      transformResponse: ({ children }: ChildrenResponse) => children,
    }),
    holidays: builder.query<holidays.Holiday[], number>({
      query: (year) => ({ url: `holidays?year=${year}` }),
    }),
  }),
});

export const {
  useListQuery,
  useLazyListQuery,
  useItemSummaryQuery,
  useUpgradeItemsQuery,
  useLazyItemPriceQuery,
  useLazyItemPricesQuery,
  useLazyChildrenQuery,
  useLazyHolidaysQuery,
} = futureOrdersListApi;

interface FutureOrderListArgs {
  startDate: string;
  endDate: string;
}

export interface FutureOrderListResponse {
  futureOrders: models.FutureOrderListItem[];
}

export interface ItemSummaryArgs {
  startDate: string | null | undefined;
  endDate: string | null | undefined;
}

export interface UpgradeItemsArgs {
  startDate: string;
  endDate: string;
}

export interface ItemPriceResponse {
  price: number | null;
}

export interface ItemPriceArgs {
  spireInventoryId: number | null;
  customerId: number | null | undefined;
  shipToId: number | null | undefined;
  requiredDate: string | null | undefined;
}

export interface ItemSummaryResponse {
  items: models.FutureOrderSummaryItem[];
}

export interface UpgradeItemsResponse {
  items: models.UpgradeItem[];
  attachments: models.UpgradeItemAttachment[];
  upgradeOptions: settings.UpgradeOption[];
}

export interface ProductCustomerDefaultsResponse {
  productCustomerDefaults: models.ProductCustomerDefault[];
}

export interface FutureOrderDetailResponse {
  futureOrder: models.FutureOrderDetail;
  prebooks: prebooks.PrebookDetail[];
  emails: prebooks.PrebookEmail[];
}

export interface FutureOrderCreateResponse {
  id: number;
}

export interface FutureOrderListDownloadArgs {
  items: models.FutureOrderListItem[];
}

export interface FutureOrderItemsListDownloadArgs {
  items: models.FutureOrderSummaryItem[];
}

export interface FutureOrderUpgradeItemsListDownloadArgs {
  filename: string;
  items: models.UpgradeItem[];
}

export interface DeletedResponse {
  orders: models.DeletedFutureOrder[];
}

export interface UpdateUpgradeItemPropertyArgs {
  id: number;
  fieldName: string;
  value: string | number | boolean | null;
}

interface SpireItemPrice {
  spireInventoryItemId: number;
  price: number;
}

interface SpireItemPricesArgs {
  customerId: number | null | undefined;
  shipToId: number | null | undefined;
  requiredDate: string | null | undefined;
  items: number[];
}

interface SpireItemPricesResponse {
  prices: SpireItemPrice[];
}

interface ChildrenResponse {
  children: models.FutureOrderChild[];
}
