import {
  createSlice,
  AsyncThunk,
  createAsyncThunk,
  createSelector,
} from '@reduxjs/toolkit';
import { boekestynSpacingApi } from 'api/boekestyn-spacing-service';
import * as boeks from 'api/models/boekestyns';
import { RootState } from '@/services/store';
import { ProblemDetails } from '@/utils/problem-details';
import { sortBy } from '@/utils/sort';

const sortByPlantName = sortBy('name');

export const ScheduleSpacingOrderType = 'SCHEDULE_SPACING_ORDER';
export const SortSpacingWorkOrderType = 'SORT_SPACING_WORK_ORDER';

export type SortFields = keyof boeks.SpacingOrder | 'crop' | 'size';

export const getScheduleById: AsyncThunk<
  boeks.SpacingOrder | undefined,
  string,
  { state: RootState }
> = createAsyncThunk(
  'boekestyn-spacing/getScheduleById',
  async (id, { getState, rejectWithValue }) => {
    try {
      const { boekestynSpacingAdmin: boekestynSpacing } =
          getState() as RootState,
        orders = boekestynSpacing.orders,
        order = orders.find((o) => o._id === id);

      return order;
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

interface MoveItemArgs {
  scheduleId: number;
  movingItem: boeks.SpacingWorkOrder;
  existingItem: boeks.SpacingWorkOrder;
}

export const moveItem: AsyncThunk<
  boeks.SpacingWorkOrder[] | undefined,
  MoveItemArgs,
  { state: RootState }
> = createAsyncThunk(
  'boekestyn-spacing/moveItem',
  async (
    { scheduleId, movingItem, existingItem },
    { getState, rejectWithValue }
  ) => {
    try {
      const { boekestynSpacingAdmin: boekestynSpacing } =
          getState() as RootState,
        schedule = boekestynSpacing.schedules.find((s) => s.id === scheduleId);

      if (schedule) {
        const copy = schedule.workOrders
            .filter((i) => i.id !== movingItem.id)
            .map((i) => ({ ...i })),
          index = copy.findIndex((i) => i.id === existingItem.id);

        if (movingItem) {
          copy.splice(index, 0, { ...movingItem });

          copy.forEach((i, index) => (i.sortOrder = index + 1));

          return copy;
        }
      }
    } catch (e) {
      return rejectWithValue(e as ProblemDetails);
    }
  }
);

interface SpacingState {
  orders: boeks.SpacingOrder[];
  lines: boeks.SpacingLine[];
  schedules: boeks.SpacingSchedule[];
  sort: SortFields;
  sortDescending?: boolean;
  plant: string | null;
}

const initialState: SpacingState = {
  orders: [],
  lines: [],
  schedules: [],
  sort: 'fullSpaceDate',
  sortDescending: false,
  plant: null,
};

export interface SortArgs {
  sort: SortFields;
  sortDescending?: boolean;
}

const spacingSlice = createSlice({
  name: 'spacing',
  initialState,
  reducers: {
    setSort(state, { payload }: { payload: SortArgs }) {
      state.sort = payload.sort;
      state.sortDescending = payload.sortDescending;
    },
    setPlant(state, { payload }: { payload: string }) {
      state.plant = payload;
    },
  },
  extraReducers: (builder) =>
    builder
      .addMatcher(
        boekestynSpacingApi.endpoints.spacing.matchFulfilled,
        (state, { payload }) => {
          state.lines = payload.lines;
        }
      )
      .addMatcher(
        boekestynSpacingApi.endpoints.spacingOrders.matchFulfilled,
        (state, { payload }) => {
          state.orders = payload.orders;
        }
      )
      .addMatcher(
        boekestynSpacingApi.endpoints.spacingSchedules.matchFulfilled,
        (state, { payload }) => {
          state.schedules = payload.schedules;
        }
      ),
});

export const { setSort, setPlant } = spacingSlice.actions;

const selectAllOrders = ({
  boekestynSpacingAdmin: boekestynSpacing,
}: RootState) => boekestynSpacing.orders;
export const selectLines = ({
  boekestynSpacingAdmin: boekestynSpacing,
}: RootState) => boekestynSpacing.lines;
export const selectSchedules = ({
  boekestynSpacingAdmin: boekestynSpacing,
}: RootState) => boekestynSpacing.schedules;
export const selectSort = ({
  boekestynSpacingAdmin: boekestynSpacing,
}: RootState) => boekestynSpacing.sort;
export const selectSortDescending = ({
  boekestynSpacingAdmin: boekestynSpacing,
}: RootState) => boekestynSpacing.sortDescending;
export const selectPlant = ({
  boekestynSpacingAdmin: boekestynSpacing,
}: RootState) => boekestynSpacing.plant;

export const selectPlants = createSelector(selectAllOrders, (orders) => {
  const plants = orders
    .reduce((memo, o) => {
      if (!memo.some((p) => p._id === o.plant._id)) {
        memo.push(o.plant);
      }
      return memo;
    }, [] as boeks.StickingOrderPlant[])
    .sort(sortByPlantName);
  return [...new Set(plants)];
});

export const selectOrders = createSelector(
  selectAllOrders,
  selectSort,
  selectSortDescending,
  selectPlant,
  (orders, sort, sortDescending, plant) => {
    const sortFn = (a: boeks.SpacingOrder, b: boeks.SpacingOrder) => {
      if (sort === 'crop') {
        return (
          a.plant.crop.localeCompare(b.plant.crop) * (sortDescending ? -1 : 1)
        );
      } else if (sort === 'size') {
        return (
          a.plant.size.localeCompare(b.plant.size) * (sortDescending ? -1 : 1)
        );
      } else {
        return sortBy(sort, sortDescending ? 'descending' : '')(a, b);
      }
    };
    return orders
      .filter((o) => !plant || o.plant._id === plant)
      .map((o) => ({ ...o }))
      .sort(sortFn);
  }
);

export default spacingSlice.reducer;
