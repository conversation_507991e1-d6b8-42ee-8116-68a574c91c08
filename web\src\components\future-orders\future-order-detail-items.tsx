import { Fragment, useRef, useState } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import * as HeadlessUI from '@headlessui/react';
import { prebooksApi, useOpenBlanketItemsQuery } from 'api/prebooks-service';
import { useInventoryItemsQuery, useVendorsQuery } from 'api/spire-service';
import * as boeks from 'api/models/boekestyns';
import * as futureOrders from 'api/models/future-orders';
import * as spire from 'api/models/spire';
import { usePermissions } from '@/services/auth';
import { useAppDispatch, useAppSelector } from '@/services/hooks';
import { DropdownMenu } from '@/components/drop-down-menu';
import { Icon } from '@/components/icon';
import { classNames } from '@/utils/class-names';
import { handleFocus } from '@/utils/focus';
import { formatCurrency } from '@/utils/format';
import { ProblemDetails } from '@/utils/problem-details';
import {
  selectItems,
  selectFutureOrder,
  InventoryItemWithDefaults,
  selectCustomerItemCodeDefaults,
  selectShipTo,
  selectCustomer,
  addItem,
  setAllWeightsAndMeasures,
  setAllUseAvailabilityPricing,
  setAllDateCodes,
  setAllHasPotCovers,
  setAllPotCovers,
  setAllRetails,
  setAllUpcs,
  setAllUnitPrices,
  selectRequiredDate,
  selectSeasonName,
  setAllExpanded,
  selectShowInventoryDialog,
  setShowInventoryDialog,
  selectShowAddFromOrder,
  selectShowSplitOrder,
  setShowAddFromOrder,
  setShowSplitOrder,
  selectPotCovers,
  setItemBoekestynProducts,
  selectAvailabilityUnitPrices,
  setError,
  selectDefaultVendorOverrides,
} from './future-order-detail-slice';
import {
  setCustomerFilter,
  setShipToFilter,
} from '@/components/future-orders/add-from-order-slice';
import { setItems } from './split-order-slice';
import { BoekestynProducts } from '@/components/future-orders/boekestyn-products';
import { AddFromOrder } from '@/components/future-orders/add-from-order';
import { SplitOrder } from '@/components/future-orders/split-order';
import { Inventory } from './inventory';
import { FutureOrderDetailItemExpanded } from './future-order-detail-item-expanded';
import { FutureOrderDetailItemCollapsed } from './future-order-detail-item-collapsed';
import { ClearItems, ClearItemsResults } from './clear-items';
import { findDefaultVendorOverride } from './item-functions';

interface FutureOrderDetailItemsProps {
  save: () => Promise<boolean>;
}

export function FutureOrderDetailItems({ save }: FutureOrderDetailItemsProps) {
  const dispatch = useAppDispatch(),
    { can } = usePermissions(),
    { data: vendors } = useVendorsQuery(),
    { data: openBlanketItemsResponse } = useOpenBlanketItemsQuery(),
    { data: inventoryItemsData } = useInventoryItemsQuery(),
    items = useAppSelector(selectItems),
    futureOrder = useAppSelector(selectFutureOrder),
    customerItemCodeDefaults = useAppSelector(selectCustomerItemCodeDefaults),
    customer = useAppSelector(selectCustomer),
    shipTo = useAppSelector(selectShipTo),
    requiredDate = useAppSelector(selectRequiredDate),
    seasonName = useAppSelector(selectSeasonName),
    showInventoryDialog = useAppSelector(selectShowInventoryDialog),
    showSplitOrder = useAppSelector(selectShowSplitOrder),
    showAddFromOrder = useAppSelector(selectShowAddFromOrder),
    potCovers = useAppSelector(selectPotCovers),
    availabilityUnitPrices = useAppSelector(selectAvailabilityUnitPrices),
    defaultVendorOverrides = useAppSelector(selectDefaultVendorOverrides),
    [allWeightsAndMeasuresSelected, setAllWeightsAndMeasuresSelected] =
      useState(false),
    [allHasPotCover, setAllHasPotCover] = useState(false),
    [allPotCover, setAllPotCover] = useState(''),
    [allDateCode, setAllDateCode] = useState(''),
    [allAvailabilityPricingSelected, setAllAvailabilityPricingSelected] =
      useState(false),
    [showExpandAllTooltip, setShowExpandAllTooltip] = useState(false),
    [showCollapseAllTooltip, setShowCollapseAllTooltip] = useState(false),
    [showClearAllTooltip, setShowClearAllTooltip] = useState(false),
    [showClearAllDialog, setShowClearAllDialog] = useState(false),
    potCoverRef = useRef<HTMLInputElement | null>(null),
    blanketItems = openBlanketItemsResponse?.blanketItems || [],
    productDefaults = inventoryItemsData?.productDefaults || [],
    totalCases = items.reduce((total, i) => total + (i.orderQuantity || 0), 0),
    totalDollars = items.reduce((total, i) => {
      const unitPrice = i.useAvailabilityPricing
        ? availabilityUnitPrices[i.id]
        : i.unitPrice;
      return total + (i.orderQuantity || 0) * (unitPrice || 0);
    }, 0),
    readonly = !can('Sales Team');

  const handleAllWeightsAndMeasuresSelectedChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const selected = e.target.checked;
    setAllWeightsAndMeasuresSelected(selected);
  };

  const handleAllWeightsAndMeasuresClick = () => {
    dispatch(setAllWeightsAndMeasures(allWeightsAndMeasuresSelected));
  };

  const handleAllUseAvailabilityPricingSelectedChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const selected = e.target.checked;
    setAllAvailabilityPricingSelected(selected);
  };

  const handleAllUseAvailabilityPricingClick = () => {
    dispatch(setAllUseAvailabilityPricing(allAvailabilityPricingSelected));
  };

  const handleAllHasPotCoverSelectedChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const selected = e.target.checked;
    setAllHasPotCover(selected);

    if (selected) {
      setAllPotCover('PC');
      window.setTimeout(() => potCoverRef.current?.focus(), 100);
    } else {
      setAllPotCover('');
    }
  };

  const handleAllPotCoverChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toUpperCase();
    setAllPotCover(value);
  };

  const handleAllPotCoverSelected = (value: string) => {
    setAllPotCover(value);
  };

  const handleAllPotCoverClick = () => {
    dispatch(setAllHasPotCovers(allHasPotCover));
    dispatch(setAllPotCovers(allPotCover));
  };

  const handleAllDateCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toUpperCase();
    setAllDateCode(value);
  };

  const handleAllDateCodeClick = () => {
    dispatch(setAllDateCodes(allDateCode));
  };

  const handleExpandAllTooltipMouseEnter = () => {
    setShowExpandAllTooltip(true);
  };

  const handleExpandAllTooltipMouseLeave = () => {
    setShowExpandAllTooltip(false);
  };

  const handleExpandAllClick = () => {
    dispatch(setAllExpanded(true));
  };

  const handleCollapseAllTooltipMouseEnter = () => {
    setShowCollapseAllTooltip(true);
  };

  const handleCollapseAllTooltipMouseLeave = () => {
    setShowCollapseAllTooltip(false);
  };

  const handleCollapseAllClick = () => {
    dispatch(setAllExpanded(false));
  };

  const handleClearAllTooltipMouseEnter = () => {
    setShowClearAllTooltip(true);
  };

  const handleClearAllTooltipMouseLeave = () => {
    setShowClearAllTooltip(false);
  };

  const handleClearAllClick = () => {
    setShowClearAllDialog(true);
  };

  const handleClearAllCancel = () => {
    setShowClearAllDialog(false);
  };

  const handleClearAllConfirm = (results: ClearItemsResults) => {
    const {
      upc,
      upcValue,
      pricing,
      dateCode,
      retail,
      weightsAndMeasures,
      potCover,
    } = results;
    if (upc) {
      dispatch(setAllUpcs(upcValue));
    }
    if (pricing) {
      dispatch(setAllUnitPrices(null));
    }
    if (dateCode) {
      dispatch(setAllDateCodes(null));
    }
    if (retail) {
      dispatch(setAllRetails(null));
    }
    if (weightsAndMeasures) {
      dispatch(setAllWeightsAndMeasures(false));
    }
    if (potCover) {
      dispatch(setAllPotCovers(null));
      dispatch(setAllHasPotCovers(false));
    }
    setShowClearAllDialog(false);
  };

  const handleAddItemClick = () => {
    dispatch(setShowInventoryDialog(true));
  };

  const handleAddItemConfirm = async (
    result: spire.InventoryItem,
    blanketItemId: number | null,
    boekestynPlantId: string | null,
    boekestynCustomerAbbreviation: string | null,
    comments: string | null
  ) => {
    const item: InventoryItemWithDefaults = {
      ...result,
      blanketItemId: null,
      comments: null,
      vendorId: null,
      vendorName: null,
      boekestynPlantId: null,
      boekestynCustomerAbbreviation: null,
      useAvailabilityPricing: false,
      upgradeLabourHours: 0,
      quantityPerFinishedItem: null,
    };

    if (blanketItemId && blanketItemId > 0) {
      item.blanketItemId = blanketItemId;
    }
    if (comments) {
      item.comments = comments;
    }

    if (customer && shipTo) {
      const { default: details } = await prebooksApi.productShipToDefaults(
        item.id,
        customer.id,
        shipTo.id
      );

      if (details.id) {
        item.customerItemCode = details.customerItemCode;
        item.hasPotCover = details.hasPotCover;
        item.potCover = details.potCover;
        item.retail = details.retail;
        item.unitPrice = details.unitPrice;
        item.upc = details.upc;
        item.weightsAndMeasures = details.weightsAndMeasures;
      }
    }

    if (allHasPotCover && allPotCover) {
      item.hasPotCover = true;
      item.potCover = allPotCover;
    }

    if (allDateCode) {
      item.dateCode = allDateCode;
    }

    if (allWeightsAndMeasuresSelected) {
      item.weightsAndMeasures = true;
    }

    if (allAvailabilityPricingSelected) {
      item.useAvailabilityPricing = true;
      item.unitPrice = null;
    }

    if (item.partNo.endsWith('PC') && !item.potCover) {
      item.hasPotCover = true;
      item.potCover = 'PC';
    } else if (item.partNo.endsWith('NF')) {
      item.hasPotCover = false;
      item.potCover = null;
    }

    if (item.blanketItemId) {
      const blanketItem = blanketItems.find((i) => i.id === item.blanketItemId);

      if (blanketItem) {
        item.vendorId = blanketItem.vendorId;
        item.vendorName = blanketItem.vendorName;
      }
    } else {
      const override = findDefaultVendorOverride(
          item.partNo,
          requiredDate,
          defaultVendorOverrides
        ),
        overrideVendor = vendors?.find((v) => v.id === override?.vendorId);
      if (overrideVendor) {
        item.vendorId = overrideVendor.id;
        item.vendorName = overrideVendor.name;
      }
    }

    if (!item.vendorId && result.primaryVendor?.vendorNo) {
      const primaryVendor = vendors?.find(
        (v) => v.vendorNo === result.primaryVendor?.vendorNo
      );
      if (primaryVendor) {
        item.vendorId = primaryVendor.id;
        item.vendorName = primaryVendor.name;
      }
    }

    const productDefault = productDefaults.find(
        (d) => d.spireInventoryId === result.id
      ),
      isBoekestyn =
        item.vendorId === boeks.BoekestynVendorId || !!boekestynPlantId;

    if (productDefault?.upgradeLabourHours) {
      item.upgradeLabourHours = productDefault.upgradeLabourHours;
    }

    if (productDefault?.isUpgrade) {
      item.upgradeSheet = true;
    }

    if (isBoekestyn) {
      item.boekestynPlantId =
        boekestynPlantId || productDefault?.boekestynPlantId || null;
      item.boekestynCustomerAbbreviation =
        boekestynCustomerAbbreviation ||
        productDefault?.boekestynCustomerAbbreviation ||
        boeks.WeeklyCustomerAbbreviation;
      item.quantityPerFinishedItem =
        productDefault?.quantityPerFinishedItem || 0;
      item.vendorId = boeks.BoekestynVendorId;
      item.vendorName = boeks.BoekestynVendorName;
    }

    await dispatch(addItem(item));
  };

  const handleAddItemCancel = () => {
    dispatch(setShowInventoryDialog(false));
  };

  const handleAddFromExistingClick = () => {
    dispatch(setCustomerFilter(customer?.name || ''));
    dispatch(setShipToFilter(shipTo?.shipId || ''));
    dispatch(setShowAddFromOrder(true));
  };

  const handleAddFromExistingClose = () => {
    dispatch(setShowAddFromOrder(false));
  };

  const handleAddFromExistingAddItem = (
    detail: futureOrders.FutureOrderDetailItem
  ) => {
    if (
      detail.spireInventoryId &&
      detail.spirePartNumber &&
      detail.description
    ) {
      const item = {
        id: detail.spireInventoryId,
        partNo: detail.spirePartNumber,
        description: detail.description,
        uom: {
          location: detail.phytoRequired ? spire.PhytoLocation : '',
        },
        dateCode: null,
        hasPotCover: detail.hasPotCover,
        potCover: detail.potCover,
        upc: detail.upc,
        weightsAndMeasures: detail.weightsAndMeasures,
        retail: detail.retail,
        unitPrice: detail.unitPrice,
        customerItemCode: detail.customerItemCode,
        upgradeSheet: detail.upgradeSheet,
        phytoRequired: detail.phytoRequired,
        blanketItemId: detail.blanketItemId,
        comments: detail.comments,
        vendorId: detail.vendorId,
        vendorName: detail.vendorName,
        boekestynPlantId: detail.boekestynPlantId,
        boekestynCustomerAbbreviation: detail.boekestynCustomerAbbreviation,
        quantityPerFinishedItem: detail.quantityPerFinishedItem,
        useAvailabilityPricing: detail.useAvailabilityPricing,
        specialPrice: detail.specialPrice,
        growerItemNotes: detail.growerItemNotes,
      };

      dispatch(addItem(item));
    }
  };

  const handleSplitOrderClick = async () => {
    try {
      if (await save()) {
        if (futureOrder) {
          // get a list of all the Future Order Item Ids in the prebook items
          const splitableItems = futureOrder.items.filter(
            (i) => !!i.orderQuantity
          );

          dispatch(setItems(splitableItems));
          dispatch(setShowSplitOrder(true));
        }
      }
    } catch (e) {
      dispatch(setError(e as ProblemDetails));
    }
  };

  const handleSplitOrderClose = () => {
    dispatch(setShowSplitOrder(false));
  };

  const handleBoekestynProductSaved = (
    itemId: number,
    value: futureOrders.FutureOrderBoekestynProduct[]
  ) => {
    dispatch(setItemBoekestynProducts({ itemId, value }));
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="mt-4 rounded-lg shadow ring-1 ring-black ring-opacity-5">
        <table className="mb-4 min-w-full divide-y divide-gray-300">
          <thead className="bg-gray-50">
            <tr>
              <th
                colSpan={2}
                className="py-2 pl-4 pr-1 text-left align-middle font-normal text-gray-700"
              >
                <HeadlessUI.Popover
                  className="relative inline-block cursor-pointer"
                  onMouseEnter={handleCollapseAllTooltipMouseEnter}
                  onMouseLeave={handleCollapseAllTooltipMouseLeave}
                >
                  <>
                    <button
                      type="button"
                      className="btn-secondary h-8 w-8 px-2 py-1"
                      onClick={handleCollapseAllClick}
                    >
                      <Icon icon="arrows-to-line" />
                    </button>
                    <HeadlessUI.Transition
                      as={Fragment}
                      show={showCollapseAllTooltip}
                      enter="transition ease-out duration-200"
                      enterFrom="opacity-0"
                      enterTo="opacity-100"
                      leave="transition ease-in duration-150"
                      leaveFrom="opacity-100"
                      leaveTo="opacity-0"
                    >
                      <HeadlessUI.Popover.Panel
                        static
                        className="absolute left-1/2 top-0 z-10 -translate-x-1/2 -translate-y-full transform bg-yellow-50"
                      >
                        <div className="w-auto whitespace-nowrap rounded-lg border p-4 shadow-lg">
                          Collapse All Items
                        </div>
                      </HeadlessUI.Popover.Panel>
                    </HeadlessUI.Transition>
                  </>
                </HeadlessUI.Popover>
                <HeadlessUI.Popover
                  className="relative inline-block cursor-pointer"
                  onMouseEnter={handleExpandAllTooltipMouseEnter}
                  onMouseLeave={handleExpandAllTooltipMouseLeave}
                >
                  <>
                    <button
                      type="button"
                      className="btn-secondary h-8 w-8 px-2 py-1"
                      onClick={handleExpandAllClick}
                    >
                      <Icon icon="arrows-from-line" />
                    </button>
                    <HeadlessUI.Transition
                      as={Fragment}
                      show={showExpandAllTooltip}
                      enter="transition ease-out duration-200"
                      enterFrom="opacity-0"
                      enterTo="opacity-100"
                      leave="transition ease-in duration-150"
                      leaveFrom="opacity-100"
                      leaveTo="opacity-0"
                    >
                      <HeadlessUI.Popover.Panel
                        static
                        className="absolute left-1/2 top-0 z-10 -translate-x-1/2 -translate-y-full transform bg-yellow-50"
                      >
                        <div className="w-auto whitespace-nowrap rounded-lg border p-4 shadow-lg">
                          Expand All Items
                        </div>
                      </HeadlessUI.Popover.Panel>
                    </HeadlessUI.Transition>
                  </>
                </HeadlessUI.Popover>

                <HeadlessUI.Popover
                  className="relative inline-block cursor-pointer"
                  onMouseEnter={handleClearAllTooltipMouseEnter}
                  onMouseLeave={handleClearAllTooltipMouseLeave}
                >
                  <>
                    <button
                      type="button"
                      className="btn-secondary ml-4 h-8 w-8 px-2 py-1 text-red-600"
                      onClick={handleClearAllClick}
                    >
                      <Icon icon="trash-can-xmark" />
                    </button>
                    <HeadlessUI.Transition
                      as={Fragment}
                      show={showClearAllTooltip}
                      enter="transition ease-out duration-200"
                      enterFrom="opacity-0"
                      enterTo="opacity-100"
                      leave="transition ease-in duration-150"
                      leaveFrom="opacity-100"
                      leaveTo="opacity-0"
                    >
                      <HeadlessUI.Popover.Panel
                        static
                        className="absolute left-1/2 top-0 z-10 -translate-x-1/4 -translate-y-full transform bg-yellow-50"
                      >
                        <div className="w-auto whitespace-nowrap rounded-lg border p-4 shadow-lg">
                          <div className="semibold">
                            Clear all item information
                          </div>
                          <div className="text-xs italic">
                            Allows you to selectively clear Pot Cover, UPC,
                            Price, Date Code, Retail and W&M information.
                          </div>
                        </div>
                      </HeadlessUI.Popover.Panel>
                    </HeadlessUI.Transition>
                  </>
                </HeadlessUI.Popover>
              </th>
              <th
                colSpan={2}
                className="px-1 py-2 text-left font-normal text-gray-500"
              >
                {!readonly && (
                  <div className="grid grid-cols-2">
                    <label className="text-left text-xs italic">
                      <HeadlessUI.Popover className="relative inline-block">
                        <HeadlessUI.Popover.Button
                          className="btn-secondary mb-1 mr-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                          tabIndex={-1}
                        >
                          <Icon icon="question-circle" />
                        </HeadlessUI.Popover.Button>
                        <HeadlessUI.Transition
                          as={Fragment}
                          enter="transition ease-out duration-200"
                          enterFrom="opacity-0 translate-y-1"
                          enterTo="opacity-100 translate-y-0"
                          leave="transition ease-in duration-150"
                          leaveFrom="opacity-100 translate-y-0"
                          leaveTo="opacity-0 translate-y-1"
                        >
                          <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 w-96 transform px-4">
                            <div className="rounded-lg bg-yellow-50 p-4 font-normal not-italic shadow-lg ring-1 ring-black ring-opacity-5">
                              <p className="font-semibold">
                                Pot Covers for new items will be set with the
                                following priority:
                              </p>
                              <ol className="ml-4 list-decimal">
                                <li>
                                  If the Product Number ends in{' '}
                                  <span className="italic">
                                    &quote;NF&quote;
                                  </span>{' '}
                                  or the Description contains
                                  <span className="italic">
                                    &quote;Planter&quote;
                                  </span>{' '}
                                  it will{' '}
                                  <span className="font-semibold">not</span> be
                                  given a Pot Cover.
                                </li>
                                <li>
                                  If a Pot Cover is set here in the header, it
                                  will be used.
                                </li>
                                {!!shipTo && (
                                  <li>
                                    If the product has a default for{' '}
                                    {shipTo.shipId}, it will be used.
                                  </li>
                                )}
                                <li>
                                  If the Product Number ends in{' '}
                                  <span className="italic">
                                    &quote;PC&quote;
                                  </span>{' '}
                                  , a{' '}
                                  <span className="italic">
                                    &quote;PC&quote;
                                  </span>{' '}
                                  pot cover will be added.
                                </li>
                              </ol>
                              <p className="mt-2 font-semibold">
                                Click the{' '}
                                <span className="border p-1">
                                  <Icon icon="arrow-down" />
                                </span>{' '}
                                button to use the selected Pot Cover for all
                                existing items.
                              </p>
                            </div>
                          </HeadlessUI.Popover.Panel>
                        </HeadlessUI.Transition>
                      </HeadlessUI.Popover>
                      Pot Cover
                    </label>
                    <label className="whitespace-nowrap text-center text-xs font-normal italic text-gray-500">
                      <HeadlessUI.Popover className="relative inline-block whitespace-normal">
                        <HeadlessUI.Popover.Button
                          className="btn-secondary mb-1 mr-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                          tabIndex={-1}
                        >
                          <Icon icon="question-circle" />
                        </HeadlessUI.Popover.Button>
                        <HeadlessUI.Transition
                          as={Fragment}
                          enter="transition ease-out duration-200"
                          enterFrom="opacity-0 translate-y-1"
                          enterTo="opacity-100 translate-y-0"
                          leave="transition ease-in duration-150"
                          leaveFrom="opacity-100 translate-y-0"
                          leaveTo="opacity-0 translate-y-1"
                        >
                          <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 w-96 -translate-x-1/2 transform px-4">
                            <div className="rounded-lg bg-yellow-50 p-4 font-normal not-italic shadow-lg ring-1 ring-black ring-opacity-5">
                              <p className="">
                                If this checkbox is checked, all new items will
                                have Use Availability Pricing checked.
                              </p>
                              <p className="mt-2">
                                To turn Use Availability Pricing on or off for
                                all existing items, click the{' '}
                                <span className="border p-1">
                                  <Icon icon="arrow-down" />
                                </span>{' '}
                                button.
                              </p>
                            </div>
                          </HeadlessUI.Popover.Panel>
                        </HeadlessUI.Transition>
                      </HeadlessUI.Popover>
                      Use Availability Pricing
                    </label>
                    <div className="flex max-w-[200px] flex-row rounded-md border border-gray-300 bg-white text-center shadow-sm">
                      <div className="flex items-center px-2">
                        <input
                          type="checkbox"
                          checked={allHasPotCover}
                          onChange={handleAllHasPotCoverSelectedChange}
                          className="h-4 w-4 rounded border-gray-300 text-blue-600 ring-0 focus:ring-0"
                        />
                      </div>
                      <input
                        type="text"
                        autoComplete="off"
                        value={allPotCover}
                        onChange={handleAllPotCoverChange}
                        onFocus={handleFocus}
                        className={classNames(
                          'flex max-w-[115px] flex-grow rounded-md border-transparent text-center text-xs shadow-sm placeholder:italic placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500',
                          allHasPotCover ? '' : 'invisible'
                        )}
                        ref={potCoverRef}
                        placeholder="PC"
                      />
                      <div
                        className={classNames(
                          'flex px-2 pt-1 font-normal',
                          allHasPotCover ? '' : 'invisible'
                        )}
                      >
                        <DropdownMenu
                          items={potCovers.map((text) => ({
                            text,
                            selected: handleAllPotCoverSelected,
                          }))}
                        />
                      </div>
                      <button
                        type="button"
                        className="btn-secondary ml-4 px-2 py-1 text-xs"
                        onClick={handleAllPotCoverClick}
                      >
                        <Icon icon="arrow-down" />
                      </button>
                    </div>
                    <div className="whitespace-nowrap text-center">
                      <input
                        type="checkbox"
                        className="h-4 w-4 rounded border-gray-300 text-blue-600 ring-0 focus:ring-0"
                        checked={allAvailabilityPricingSelected}
                        onChange={handleAllUseAvailabilityPricingSelectedChange}
                      />
                      <button
                        type="button"
                        className="btn-secondary ml-1 px-2 text-xs"
                        onClick={handleAllUseAvailabilityPricingClick}
                      >
                        <Icon icon="arrow-down" />
                      </button>
                    </div>
                  </div>
                )}
              </th>
              <th className="px-1 py-2 text-left text-xs font-normal text-gray-500">
                {!readonly && (
                  <>
                    <label className="text-left italic">
                      <HeadlessUI.Popover className="relative inline-block">
                        <HeadlessUI.Popover.Button
                          className="btn-secondary mb-1 mr-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                          tabIndex={-1}
                        >
                          <Icon icon="question-circle" />
                        </HeadlessUI.Popover.Button>
                        <HeadlessUI.Transition
                          as={Fragment}
                          enter="transition ease-out duration-200"
                          enterFrom="opacity-0 translate-y-1"
                          enterTo="opacity-100 translate-y-0"
                          leave="transition ease-in duration-150"
                          leaveFrom="opacity-100 translate-y-0"
                          leaveTo="opacity-0 translate-y-1"
                        >
                          <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 w-96 -translate-x-1/2 transform px-4">
                            <div className="rounded-lg bg-yellow-50 p-4 font-normal not-italic shadow-lg ring-1 ring-black ring-opacity-5">
                              <p className="">
                                If there is a date code here, it will be used
                                for all new items.
                              </p>
                              <p className="mt-2">
                                To use this date code for all existing items,
                                click the{' '}
                                <span className="border p-1">
                                  <Icon icon="arrow-down" />
                                </span>{' '}
                                button.
                              </p>
                            </div>
                          </HeadlessUI.Popover.Panel>
                        </HeadlessUI.Transition>
                      </HeadlessUI.Popover>
                      Date Code
                    </label>
                    <div className="whitespace-nowrap">
                      <input
                        type="text"
                        value={allDateCode}
                        onChange={handleAllDateCodeChange}
                        onFocus={handleFocus}
                        className="w-full max-w-[125px] rounded-md border-gray-300 text-center text-xs shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                      <button
                        type="button"
                        className="btn-secondary ml-1 px-2 text-xs"
                        onClick={handleAllDateCodeClick}
                      >
                        <Icon icon="arrow-down" />
                      </button>
                    </div>
                  </>
                )}
              </th>
              <th className="px-1 py-2">&nbsp;</th>
              <th className="px-1 py-2 text-left">
                {!readonly && (
                  <>
                    <label className="whitespace-nowrap text-left text-xs font-normal italic text-gray-500">
                      <HeadlessUI.Popover className="relative inline-block whitespace-normal">
                        <HeadlessUI.Popover.Button
                          className="btn-secondary mb-1 mr-1 rounded-full px-1 py-0 text-sm text-blue-500 focus:ring-0"
                          tabIndex={-1}
                        >
                          <Icon icon="question-circle" />
                        </HeadlessUI.Popover.Button>
                        <HeadlessUI.Transition
                          as={Fragment}
                          enter="transition ease-out duration-200"
                          enterFrom="opacity-0 translate-y-1"
                          enterTo="opacity-100 translate-y-0"
                          leave="transition ease-in duration-150"
                          leaveFrom="opacity-100 translate-y-0"
                          leaveTo="opacity-0 translate-y-1"
                        >
                          <HeadlessUI.Popover.Panel className="absolute bottom-10 z-10 w-96 -translate-x-1/2 transform px-4">
                            <div className="rounded-lg bg-yellow-50 p-4 font-normal not-italic shadow-lg ring-1 ring-black ring-opacity-5">
                              <p className="">
                                If this checkbox is checked, all new items will
                                have Weights & Measures checked.
                              </p>
                              <p className="mt-2">
                                To turn Weights & Measures on or off for all
                                existing items, click the{' '}
                                <span className="border p-1">
                                  <Icon icon="arrow-down" />
                                </span>{' '}
                                button.
                              </p>
                            </div>
                          </HeadlessUI.Popover.Panel>
                        </HeadlessUI.Transition>
                      </HeadlessUI.Popover>
                      W&M
                    </label>
                    <div className="whitespace-nowrap">
                      <input
                        type="checkbox"
                        className="h-4 w-4 rounded border-gray-300 text-blue-600 ring-0 focus:ring-0"
                        checked={allWeightsAndMeasuresSelected}
                        onChange={handleAllWeightsAndMeasuresSelectedChange}
                      />
                      <button
                        type="button"
                        className="btn-secondary ml-1 px-2 text-xs"
                        onClick={handleAllWeightsAndMeasuresClick}
                      >
                        <Icon icon="arrow-down" />
                      </button>
                    </div>
                  </>
                )}
              </th>
              <th className="px-1 py-2">&nbsp;</th>
            </tr>
          </thead>
          <tbody className="bg-white">
            {items.map((item) => (
              <Fragment key={item.id}>
                {!!item.expanded && (
                  <FutureOrderDetailItemExpanded item={item} />
                )}
                {!item.expanded && (
                  <FutureOrderDetailItemCollapsed item={item} />
                )}
              </Fragment>
            ))}
          </tbody>
          <tfoot>
            <tr>
              <td colSpan={3} className="px-1 py-2 pr-4 text-left">
                {!readonly && (
                  <>
                    <button
                      type="button"
                      className="btn-new"
                      onClick={handleAddItemClick}
                    >
                      <Icon icon="plus" />
                      &nbsp; Add Item &nbsp;
                      <span className="text-xs text-gray-300">(Alt + A)</span>
                    </button>
                    <button
                      type="button"
                      className="btn-new ml-2"
                      onClick={handleAddFromExistingClick}
                    >
                      <Icon icon="layer-plus" />
                      &nbsp; Add From Existing &nbsp;
                      <span className="text-xs text-gray-300">(Alt + X)</span>
                    </button>
                    {!!items.length && (
                      <button
                        type="button"
                        className="btn-primary ml-2"
                        onClick={handleSplitOrderClick}
                      >
                        <Icon icon="split" />
                        &nbsp; Split Order &nbsp;
                        <span className="text-xs text-gray-300">(Alt + S)</span>
                      </button>
                    )}
                  </>
                )}
              </td>
              <th colSpan={5} className="px-1 py-2 font-bold text-gray-700">
                <div className="flex flex-row justify-end">
                  <div>Total:</div>
                  <div className="ml-2">{totalCases} Cases</div>
                  {!!totalDollars && (
                    <div className="ml-2">({formatCurrency(totalDollars)})</div>
                  )}
                </div>
              </th>
            </tr>
          </tfoot>
        </table>
        <Inventory
          open={showInventoryDialog}
          cancel={handleAddItemCancel}
          confirm={handleAddItemConfirm}
          customerItemCodeDefaults={customerItemCodeDefaults}
          customer={customer}
          shipTo={shipTo}
          requiredDate={requiredDate}
          seasonName={seasonName}
        />
        <ClearItems
          open={showClearAllDialog}
          cancel={handleClearAllCancel}
          confirm={handleClearAllConfirm}
        />
      </div>
      <AddFromOrder
        open={showAddFromOrder}
        addItem={handleAddFromExistingAddItem}
        cancel={handleAddFromExistingClose}
      />
      <SplitOrder open={showSplitOrder} onClose={handleSplitOrderClose} />
      <BoekestynProducts
        onSave={handleBoekestynProductSaved}
        productDefaults={productDefaults}
      />
    </DndProvider>
  );
}
