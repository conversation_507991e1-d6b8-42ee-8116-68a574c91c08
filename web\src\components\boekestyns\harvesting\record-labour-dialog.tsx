import { useState, Fragment, useEffect } from 'react';
import * as HeadlessUI from '@headlessui/react';
import * as boeks from 'api/models/boekestyns';
import * as models from 'api/models/boekestyns';
import { classNames } from '@/utils/class-names';
import { formatNumber } from '@/utils/format';
import { useHarvestingOrdersQuery, HarvestingLabourVariety } from 'api/boekestyn-harvesting-service';
import { formatDate } from '@/utils/format';

interface PauseLabourDialogProps {
  workOrder: boeks.HarvestingWorkOrderItem;
  open: boolean;
  onClose: () => void;
  onRecord: (args: { comments: string | null, varieties: HarvestingLabourVariety[] }) => void;
}

export function RecordLabourDialog({
  workOrder,
  open,
  onClose,
  onRecord,
}: PauseLabourDialogProps) {
  const [recordComments, setRecordComments] = useState('');
  const [harvestingOrder, setHarvestingOrder] = useState<models.HarvestingOrder | null>(null);

  // Fetch the harvesting orders data
  const today = new Date();
  const startDate = formatDate(new Date(today.getFullYear(), today.getMonth() - 1, 1), 'yyyy-MM-dd');
  const endDate = formatDate(new Date(today.getFullYear(), today.getMonth() + 2, 0), 'yyyy-MM-dd');
  const [labourVarieties, setLabourVarieties] = useState<HarvestingLabourVariety[]>([]);

  const { data: harvestingOrdersData, isLoading } = useHarvestingOrdersQuery({
    startDate,
    endDate,
  });

  // Find the matching harvesting order based on the workOrder's orderId
  useEffect(() => {
    if (harvestingOrdersData?.orders && workOrder) {
      const matchingOrder = harvestingOrdersData.orders.find(
        (order) => order._id === workOrder.orderId
      );
      if (matchingOrder) {
        setHarvestingOrder(matchingOrder);
      }
    }
  }, [harvestingOrdersData, workOrder]);

  const handleTransitionAfterEnter = () => {
    setRecordComments('');
  };

  const handlePauseChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setRecordComments(e.target.value);
  };

  const handlePauseClick = () => {
    onRecord({ comments: recordComments, varieties: labourVarieties });
    onClose();
  };

  // Helper functions to calculate pots
  function potsHarvested(order: models.HarvestingOrder, variety: string | null) {
    return order.rounds.reduce((total, round) => {
      return (
        total +
        round.varieties
          .filter((v) => !variety || variety === v.name)
          .reduce((sum, v) => v.harvested + sum, 0)
      );
    }, 0);
  }

  function potsRemaining(order: models.HarvestingOrder, variety: string | null) {
    return (
      ((variety ? order.varieties.find((v) => v.name === variety) : order)
        ?.pots || 0) - potsHarvested(order, variety) - potsThrownOut(order, variety)
    );
  }

  function potsThrownOut(order: models.HarvestingOrder, variety: string | null) {
    return order.rounds.reduce((total, round) => {
      return (
        total +
        round.varieties
          .filter((v) => !variety || variety === v.name)
          .reduce((sum, v) => v.thrownOut + sum, 0)
      );
    }, 0);
  }

  // Calculate remaining pots for a variety
  function calculateRemainingPots(varietyName: string): number {
    if (harvestingOrder) {
      return potsRemaining(harvestingOrder, varietyName);
    }
    // Fallback to the old calculation if we don't have the harvesting order data
    const variety = workOrder.varieties.find(v => v.name === varietyName);
    return variety ? variety.pots * 0.75 : 0;
  }

  // Calculate harvested pots for a variety
  function calculateHarvestedPots(varietyName: string): number {
    if (harvestingOrder) {
      return potsHarvested(harvestingOrder, varietyName);
    }
    // Fallback to the old calculation if we don't have the harvesting order data
    const variety = workOrder.varieties.find(v => v.name === varietyName);
    return variety ? variety.pots - (variety.pots * 0.75) : 0;
  }

  function handleHarvestedChange(varietyName: string, e: React.ChangeEvent<HTMLInputElement>) {
    const value = e.target.valueAsNumber;
    setLabourVarieties((prev) => {
      const variety = prev.find(v => v.varietyName === varietyName);
      if (variety) {
        variety.harvested = value;
      } else {
        prev.push({ varietyName, harvested: value, thrownOut: 0 });
      }
      return [...prev];
    });
  }

  function handleThrownOutChange(varietyName: string, e: React.ChangeEvent<HTMLInputElement>) {
    const value = e.target.valueAsNumber;
    setLabourVarieties((prev) => {
      const variety = prev.find(v => v.varietyName === varietyName);
      if (variety) {
        variety.thrownOut = value;
      } else {
        prev.push({ varietyName, harvested: 0, thrownOut: value });
      }
      return [...prev];
    });
  }


  return (
    <HeadlessUI.Transition.Root
      show={open}
      as={Fragment}
      afterEnter={handleTransitionAfterEnter}
    >
      <HeadlessUI.Dialog as="div" className="relative z-10" onClose={onClose}>
        <HeadlessUI.Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </HeadlessUI.Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-0 text-center">
            <HeadlessUI.Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-0 scale-95"
              enterTo="opacity-100 translate-y-0 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 scale-100"
              leaveTo="opacity-0 translate-y-0 scale-95"
            >
              <HeadlessUI.Dialog.Panel className="relative my-8 w-full max-w-2xl transform overflow-hidden rounded-lg bg-white p-6 text-left shadow-xl transition-all">
                <div>
                  <div className="mt-3 text-center">
                    <HeadlessUI.Dialog.Title
                      as="h3"
                      className="text-base font-semibold leading-6 text-gray-900"
                    >
                      Record Harvest
                    </HeadlessUI.Dialog.Title>
                    <form className="mt-5">
                      <div className="mt-5">
                        <div className="text-left">
                          <label>Crew Size</label>
                          <input
                            type="number"
                            name="comments"
                            className="block w-40 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                            value={3}
                          />
                        </div>
                        <table className="min-w-full divide-y divide-gray-300 text-sm">
                          <thead>
                            <tr>
                              <th className="w-1 p-2">Variety</th>
                              <th className="w-1 p-2 text-right">Planted</th>
                              <th className="w-1 p-2 text-right">Remaining </th>
                              <th className="whitespace-nowrap p-2 text-center">
                                Harvested
                              </th>
                              <th className="whitespace-nowrap p-2 text-center">
                                Throw Out
                              </th>
                              <th>&nbsp;</th>
                            </tr>
                          </thead>
                          <tbody>
                            {workOrder.varieties.map((variety) => (
                              <tr key={variety.name}>
                                <td className="w-1 whitespace-nowrap p-2 text-left">
                                  {variety.name}
                                </td>
                                <td className="w-1 p-2 text-right">
                                  {formatNumber(variety.pots, '0,0')}
                                </td>
                                <td className="w-1 p-2 text-right">
                                  {isLoading ? (
                                    <span className="italic text-gray-500">Loading...</span>
                                  ) : (
                                    formatNumber(calculateRemainingPots(variety.name), '0,0')
                                  )}
                                </td>
                                <td className="text-center">
                                  {isLoading ? (
                                    <span className="italic text-gray-500">Loading...</span>
                                  ) : (
                                    <input
                                      type="number"
                                      defaultValue={calculateHarvestedPots(variety.name)}
                                      onChange={(e) => handleHarvestedChange(variety.name, e)}
                                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    />
                                  )}
                                </td>
                                <td className="text-center">
                                  <input
                                    type="number"
                                    defaultValue={0}
                                    onChange={(e) => handleThrownOutChange(variety.name, e)}
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                  />
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>

                      <div className="text-left">
                        <label>Comments</label>
                        <textarea
                          name="comments"
                          className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                          value={recordComments}
                          onChange={handlePauseChange}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-500">
                          Harvest Complete?
                        </label>
                        <div>
                          <HeadlessUI.Switch
                            checked={false}
                            onChange={() => console.log('switch')}
                            className={classNames(
                              false ? 'bg-blue-400' : 'bg-gray-200',
                              'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                            )}
                          >
                            <span
                              aria-hidden="true"
                              className={classNames(
                                false ? 'translate-x-5' : 'translate-x-0',
                                'pointer-events-none inline-block size-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
                              )}
                            />
                          </HeadlessUI.Switch>
                        </div>
                      </div>
                    </form>
                  </div>
                </div>
                <div className="mt-6 text-right">
                  <button
                    type="button"
                    className="btn-secondary"
                    onClick={onClose}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn-primary ml-2"
                    onClick={handlePauseClick}
                  >
                    Record Harvest
                  </button>
                </div>
              </HeadlessUI.Dialog.Panel>
            </HeadlessUI.Transition.Child>
          </div>
        </div>
      </HeadlessUI.Dialog>
    </HeadlessUI.Transition.Root>
  );
}
