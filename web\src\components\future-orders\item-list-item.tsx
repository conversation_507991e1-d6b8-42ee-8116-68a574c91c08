import React, { Fragment, useState } from 'react';
import Link from 'next/link';
import * as HeadlessUI from '@headlessui/react';
import * as models from 'api/models/future-orders';
import { routes } from '@/services/routes';
import { Icon } from '@/components/icon';
import { formatDate, formatNumber, parseQuantity } from '@/utils/format';

const thisYear = new Date().getFullYear();

export function ItemListItem({
  item,
}: {
  item: models.FutureOrderSummaryItem;
}) {
  const [tooltipVisible, setTooltipVisible] = useState(false);

  return (
    <tr className="border-b-2 border-gray-100">
      <td className="whitespace-nowrap px-3 py-4 text-left text-sm font-medium">
        <Link href={routes.futureOrders.detail.to(item.futureOrderId)}>
          {formatNumber(item.futureOrderId, '00000')}
        </Link>
      </td>
      <td className="whitespace-nowrap px-3 py-4 text-left text-sm text-gray-700">
        {!!item.prebookEmailSent && (
          <HeadlessUI.Popover className="relative inline-block">
            <HeadlessUI.Popover.Button
              as="div"
              className="inline-block cursor-pointer"
              onMouseEnter={() => setTooltipVisible(true)} // Show tooltip on mouseenter
              onMouseLeave={() => setTooltipVisible(false)} // Hide tooltip on mouseleave
            >
              <Icon icon="envelope" className="mr-1" />
            </HeadlessUI.Popover.Button>
            <HeadlessUI.Transition
              as={Fragment}
              show={tooltipVisible} // Show based on tooltipVisible state
              enter="transition ease-out duration-200"
              enterFrom="opacity-0 translate-y-1"
              enterTo="opacity-100 translate-y-0"
              leave="transition ease-in duration-150"
              leaveFrom="opacity-100 translate-y-0"
              leaveTo="opacity-0 translate-y-1"
            >
              <HeadlessUI.Popover.Panel
                static
                className="absolute z-10 mt-2 w-64 rounded-md bg-white p-4 shadow-lg ring-1 ring-black ring-opacity-5"
              >
                <p className="text-xs text-gray-500">
                  Prebook email was sent to the grower.
                </p>
              </HeadlessUI.Popover.Panel>
            </HeadlessUI.Transition>
          </HeadlessUI.Popover>
        )}
        {item.spirePartNumber}
        {!!item.customerItemCode && (
          <span className="ml-2 text-gray-500">({item.customerItemCode})</span>
        )}
      </td>
      <td className="whitespace-nowrap px-3 py-4 text-left text-sm text-gray-700">
        {formatDate(item.date, 'MMM d')}
        {!isCurrentYear(item.date) && `, ${formatDate(item.date, 'yyyy')}`}
      </td>
      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
        {item.season}
      </td>
      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
        {item.customer}
        {!!item.shipTo && (
          <>
            <br />
            <span className="italic text-gray-400">{item.shipTo}</span>
          </>
        )}
      </td>
      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
        {item.salesperson}
      </td>
      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-700">
        {item.vendor}
      </td>
      <td className="whitespace-nowrap px-3 py-4 text-center text-sm text-gray-700">
        {formatNumber(item.orderQuantity)}
      </td>
      <td className="whitespace-nowrap px-3 py-4 text-center text-sm font-medium">
        {(item.prebookIdList.split(',') || [])
          .map(parseQuantity)
          .map((prebookId) =>
            prebookId ? (
              <Link key={prebookId} href={routes.prebooks.detail.to(prebookId)}>
                {formatNumber(prebookId, '00000')}
              </Link>
            ) : (
              <span key={prebookId}>No Prebook</span>
            )
          )}
      </td>
    </tr>
  );
}

function isCurrentYear(date: string | null) {
  if (date == null || date.length < 4) {
    return false;
  }

  const year = parseQuantity(date.substring(0, 4));

  return year === thisYear;
}
